#!/usr/bin/env python3
"""
简单的上下文记忆测试脚本
测试您提到的具体场景：深圳儿童医院停车 -> 吃饭
"""

import requests
import json
import time

API_BASE = "http://localhost:8000"

def test_context_scenario():
    """测试具体的上下文场景"""
    print("🧪 测试上下文记忆场景：深圳儿童医院相关问题")
    print("=" * 60)
    
    # 1. 创建会话
    print("1. 创建新会话...")
    response = requests.post(f"{API_BASE}/api/session/create", json={})
    if response.status_code == 200:
        session_data = response.json()
        session_id = session_data["session_id"]
        print(f"✅ 会话创建成功: {session_id}")
    else:
        print(f"❌ 会话创建失败: {response.status_code}")
        return
    
    # 2. 测试对话序列
    conversations = [
        {
            "message": "深圳儿童医院停车方便吗？",
            "description": "询问深圳儿童医院停车情况"
        },
        {
            "message": "吃饭呢？",
            "description": "询问深圳儿童医院附近吃饭情况（应该能理解上下文）"
        },
        {
            "message": "那里的医生怎么样？",
            "description": "询问深圳儿童医院的医生情况（测试指代理解）"
        },
        {
            "message": "挂号容易吗？",
            "description": "询问深圳儿童医院挂号情况（测试隐含上下文）"
        }
    ]
    
    print(f"\n2. 开始对话测试...")
    
    for i, conv in enumerate(conversations, 1):
        print(f"\n--- 第{i}轮对话 ---")
        print(f"📝 测试目的: {conv['description']}")
        print(f"👤 用户: {conv['message']}")
        
        # 发送消息
        response = requests.post(
            f"{API_BASE}/api/chat",
            json={
                "message": conv["message"],
                "session_id": session_id
            },
            stream=True,
            timeout=30
        )
        
        if response.status_code == 200:
            ai_response = ""
            
            # 处理流式响应
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith("data: "):
                        data = line[6:]
                        if data.strip():
                            try:
                                parsed = json.loads(data)
                                if parsed["type"] == "chunk":
                                    ai_response += parsed["data"].get("content", "")
                                elif parsed["type"] == "complete":
                                    ai_response = parsed["data"].get("full_response", ai_response)
                                    break
                                elif parsed["type"] == "error":
                                    print(f"❌ AI回复错误: {parsed['data']['message']}")
                                    break
                            except json.JSONDecodeError:
                                continue
            
            print(f"🤖 AI: {ai_response}")
            
            # 分析上下文理解情况
            if i == 1:
                # 第一轮，检查是否回答了停车问题
                if any(keyword in ai_response for keyword in ["停车", "车位", "停车场", "停车费"]):
                    print("✅ 正确理解了停车相关问题")
                else:
                    print("⚠️ 可能没有正确理解停车问题")
                    
            elif i == 2:
                # 第二轮，检查是否理解"吃饭"是指深圳儿童医院附近的餐饮
                if any(keyword in ai_response for keyword in ["深圳儿童医院", "医院附近", "周边", "餐厅", "食堂"]):
                    print("✅ 正确理解了上下文，知道是问医院附近吃饭")
                else:
                    print("❌ 没有理解上下文，不知道是问医院附近吃饭")
                    
            elif i == 3:
                # 第三轮，检查是否理解"那里"指的是深圳儿童医院
                if any(keyword in ai_response for keyword in ["深圳儿童医院", "该医院", "这家医院"]):
                    print("✅ 正确理解了指代关系，知道'那里'指深圳儿童医院")
                else:
                    print("❌ 没有理解指代关系")
                    
            elif i == 4:
                # 第四轮，检查是否理解挂号是指深圳儿童医院的挂号
                if any(keyword in ai_response for keyword in ["深圳儿童医院", "该医院", "这家医院"]):
                    print("✅ 正确理解了隐含上下文，知道是问医院挂号")
                else:
                    print("❌ 没有理解隐含上下文")
        else:
            print(f"❌ 请求失败: {response.status_code}")
        
        # 等待一下再进行下一轮
        time.sleep(1)
    
    # 3. 检查会话历史
    print(f"\n3. 检查会话历史...")
    response = requests.get(f"{API_BASE}/api/session/{session_id}/messages")
    if response.status_code == 200:
        messages = response.json()
        print(f"✅ 会话历史包含 {len(messages)} 条消息")
        
        user_messages = [msg for msg in messages if msg["role"] == "user"]
        ai_messages = [msg for msg in messages if msg["role"] == "assistant"]
        
        print(f"📊 用户消息: {len(user_messages)} 条")
        print(f"📊 AI回复: {len(ai_messages)} 条")
        
        if len(user_messages) == 4 and len(ai_messages) == 4:
            print("✅ 消息保存正常")
        else:
            print("⚠️ 消息保存可能有问题")
    else:
        print(f"❌ 获取会话历史失败: {response.status_code}")
    
    print(f"\n🎉 上下文记忆测试完成！")
    print("=" * 60)

def main():
    """主函数"""
    try:
        # 检查服务器状态
        response = requests.get(f"{API_BASE}/api/health")
        if response.status_code == 200:
            print("✅ 服务器连接正常")
            test_context_scenario()
        else:
            print("❌ 服务器连接失败")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
