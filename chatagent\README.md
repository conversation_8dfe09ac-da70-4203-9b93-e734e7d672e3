# 🤖 智能聊天系统

基于 **AutoGen 0.7.6 + DeepSeek** 的智能聊天系统，采用 **Gemini 风格界面设计**，支持 **SSE 流式输出**。

## ✨ 特性

- 🎯 **真实AI对话**: 基于AutoGen 0.7.6 + DeepSeek API，非模拟模式
- 🔄 **流式输出**: 使用SSE协议实现实时流式对话体验
- 🎨 **Gemini风格**: 美观大方的现代化界面设计
- ⚡ **高性能**: FastAPI后端，响应迅速
- 📱 **响应式**: 支持桌面和移动设备
- 🛠️ **易部署**: 一键启动脚本，自动处理依赖

## 🏗️ 技术架构

### 后端技术栈
- **FastAPI**: 现代化Python Web框架
- **AutoGen 0.7.6**: 微软开源的多智能体对话框架
- **DeepSeek API**: 高性能大语言模型
- **SSE**: Server-Sent Events 流式通信
- **Uvicorn**: ASGI服务器

### 前端技术栈
- **原生JavaScript**: 无框架依赖，轻量高效
- **CSS3**: 现代化样式，Gemini设计风格
- **Material Icons**: Google图标库
- **EventSource**: 原生SSE客户端

## 📁 项目结构

```
chatagent/
├── backend/                 # 后端代码
│   ├── main.py             # FastAPI主服务器
│   ├── chat_service.py     # AutoGen聊天服务
│   ├── config.py           # 配置文件
│   └── requirements.txt    # Python依赖
├── frontend/               # 前端代码
│   ├── index.html         # 主页面
│   ├── css/
│   │   └── style.css      # Gemini风格样式
│   └── js/
│       └── chat.js        # 聊天逻辑
├── start_server.py        # 启动脚本
└── README.md             # 说明文档
```

## 🚀 快速开始

### 方法一：一键启动（推荐）

```bash
# 1. 进入项目目录
cd chatagent

# 2. 安装依赖
python install.py

# 3. 配置API密钥（编辑 backend/config.py）
# DEEPSEEK_API_KEY = "sk-your-actual-api-key-here"

# 4. 测试系统
python test_system.py

# 5. 启动系统
python run.py
```

### 方法二：手动安装

#### 1. 环境要求
- Python 3.8+
- DeepSeek API密钥

#### 2. 获取API密钥
1. 访问 [DeepSeek平台](https://platform.deepseek.com/)
2. 注册账号并获取API密钥
3. 复制API密钥（格式：sk-xxxxxxxxxxxxxxxx）

#### 3. 安装依赖
```bash
cd chatagent/backend
pip install -r requirements.txt
```

#### 4. 配置API密钥
编辑 `backend/config.py` 文件：
```python
# 将您的API密钥替换到这里
DEEPSEEK_API_KEY = "sk-your-actual-api-key-here"
```

#### 5. 启动系统
```bash
# 方式1：使用简化启动脚本
python run.py

# 方式2：直接启动uvicorn
cd backend
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 访问系统

- **聊天界面**: http://localhost:8000/static/index.html
- **API文档**: http://localhost:8000/docs
- **系统状态**: http://localhost:8000/api/health

## 🎮 使用指南

### 基本功能

1. **发送消息**: 在输入框输入消息，点击发送按钮或按Enter键
2. **流式对话**: AI回复会实时流式显示，提供更好的交互体验
3. **清空对话**: 点击清空按钮可以清除所有对话记录
4. **系统设置**: 点击设置按钮可以调整主题、字体等

### 快捷键

- `Enter`: 发送消息
- `Shift + Enter`: 换行
- `Ctrl + C`: 停止服务器（在终端中）

### 界面功能

- **状态指示器**: 显示系统连接状态
- **字符计数**: 实时显示输入字符数
- **消息时间**: 每条消息显示发送时间
- **响应式设计**: 自适应不同屏幕尺寸

## 🔧 配置说明

### 系统配置 (config.py)

```python
# DeepSeek API配置
DEEPSEEK_API_KEY = "your-api-key"
DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1"
DEEPSEEK_MODEL = "deepseek-chat"

# 服务器配置
SYSTEM_CONFIG = {
    "host": "0.0.0.0",
    "port": 8000,
    "debug": True
}

# AI配置
AI_CONFIG = {
    "temperature": 0.7,
    "max_tokens": 4000,
    "timeout": 120,
    "stream": True
}
```

### AutoGen配置

系统使用AutoGen 0.5.7的AssistantAgent和UserProxyAgent：

```python
# 智能助手代理
assistant_agent = AssistantAgent(
    name="智能助手",
    llm_config=llm_config,
    system_message="专业的AI助手系统消息..."
)

# 用户代理
user_proxy = UserProxyAgent(
    name="用户",
    human_input_mode="NEVER",
    max_consecutive_auto_reply=1,
    code_execution_config=False
)
```

## 🔌 API接口

### 聊天接口

```http
POST /api/chat
Content-Type: application/json

{
    "message": "用户消息",
    "session_id": "可选的会话ID"
}
```

返回SSE流式响应：

```javascript
// 连接事件
data: {"type": "connected", "data": {"message": "连接成功"}}

// 开始事件
data: {"type": "start", "data": {"message": "AI正在思考..."}}

// 流式内容
data: {"type": "chunk", "data": {"content": "部分回复内容"}}

// 完成事件
data: {"type": "complete", "data": {"full_response": "完整回复"}}

// 结束事件
data: {"type": "end", "data": {"message": "对话结束"}}
```

### 其他接口

- `GET /api/health` - 健康检查
- `GET /api/history` - 获取对话历史
- `POST /api/clear` - 清空对话历史
- `GET /api/status` - 系统状态

## 🎨 界面设计

### Gemini风格特点

- **现代化配色**: 采用Google Material Design配色方案
- **圆角设计**: 大量使用圆角元素，视觉柔和
- **渐变背景**: 优雅的渐变色背景
- **流畅动画**: 丰富的CSS动画效果
- **响应式布局**: 适配各种屏幕尺寸

### 主要颜色

```css
--primary-color: #1a73e8;    /* 主色调 */
--secondary-color: #34a853;  /* 辅助色 */
--accent-color: #fbbc04;     /* 强调色 */
--error-color: #ea4335;      /* 错误色 */
```

## 🛠️ 开发指南

### 本地开发

```bash
# 安装依赖
pip install -r backend/requirements.txt

# 启动开发服务器
cd backend
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 自定义配置

1. **修改AI系统消息**: 编辑 `chat_service.py` 中的 `system_message`
2. **调整界面样式**: 修改 `frontend/css/style.css`
3. **扩展API功能**: 在 `main.py` 中添加新的路由
4. **自定义前端逻辑**: 修改 `frontend/js/chat.js`

### 部署建议

- **生产环境**: 使用Gunicorn + Nginx部署
- **容器化**: 可以使用Docker进行容器化部署
- **HTTPS**: 生产环境建议启用HTTPS
- **负载均衡**: 高并发场景可以使用负载均衡

## 🐛 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `config.py` 中的API密钥是否正确
   - 确认API密钥有效且有足够余额

2. **端口占用**
   - 启动脚本会自动处理端口占用
   - 手动释放：`netstat -ano | findstr :8000`

3. **依赖安装失败**
   - 升级pip：`python -m pip install --upgrade pip`
   - 使用国内镜像：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple`

4. **AutoGen导入失败**
   - 确认安装了正确版本：`pip install pyautogen==0.5.7`
   - 检查Python版本是否满足要求

### 日志查看

- 服务器日志会在终端中实时显示
- 前端错误可以通过浏览器开发者工具查看

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📞 支持

如有问题，请通过以下方式联系：

- 提交GitHub Issue
- 发送邮件至项目维护者

---

**享受与AI的智能对话体验！** 🚀
