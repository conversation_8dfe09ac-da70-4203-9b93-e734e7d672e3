#!/usr/bin/env python3
"""
智能聊天系统依赖安装脚本
"""

import sys
import subprocess
from pathlib import Path

def install_dependencies():
    """安装依赖包"""
    print("📦 智能聊天系统 - 依赖安装")
    print("=" * 40)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查requirements.txt
    backend_dir = Path(__file__).parent / "backend"
    requirements_file = backend_dir / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    print(f"📋 依赖文件: {requirements_file}")
    
    # 升级pip
    print("\n🔧 升级pip...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ], check=True)
        print("✅ pip升级完成")
    except subprocess.CalledProcessError as e:
        print(f"⚠️ pip升级失败: {e}")
    
    # 安装依赖
    print("\n📦 安装依赖包...")
    try:
        # 首先尝试安装基础依赖
        basic_deps = [
            "fastapi>=0.100.0",
            "uvicorn[standard]>=0.20.0", 
            "python-multipart>=0.0.5",
            "pydantic>=2.0.0",
            "httpx>=0.25.0",
            "python-dotenv>=1.0.0",
            "aiofiles>=23.0.0",
            "loguru>=0.7.0"
        ]
        
        print("安装基础依赖...")
        for dep in basic_deps:
            print(f"  安装: {dep}")
            subprocess.run([
                sys.executable, "-m", "pip", "install", dep
            ], check=True)
        
        # 尝试安装AutoGen
        print("\n安装AutoGen...")
        autogen_versions = [
            "pyautogen==0.5.7",
            "pyautogen>=0.2.0,<1.0.0",
            "pyautogen"
        ]
        
        autogen_installed = False
        for version in autogen_versions:
            try:
                print(f"  尝试安装: {version}")
                subprocess.run([
                    sys.executable, "-m", "pip", "install", version
                ], check=True)
                autogen_installed = True
                print(f"✅ AutoGen安装成功: {version}")
                break
            except subprocess.CalledProcessError:
                print(f"  {version} 安装失败，尝试下一个版本...")
                continue
        
        if not autogen_installed:
            print("❌ AutoGen安装失败，请手动安装")
            return False
        
        # 安装OpenAI
        print("\n安装OpenAI...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", "openai>=1.0.0"
        ], check=True)
        
        print("\n✅ 所有依赖安装完成!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        print("\n💡 手动安装建议:")
        print("pip install fastapi uvicorn[standard] pyautogen openai")
        return False

def verify_installation():
    """验证安装"""
    print("\n🔍 验证安装...")
    
    # 测试导入
    test_imports = [
        ("FastAPI", "fastapi"),
        ("Uvicorn", "uvicorn"),
        ("AutoGen", "autogen"),
        ("OpenAI", "openai"),
        ("Pydantic", "pydantic"),
        ("HTTPX", "httpx")
    ]
    
    all_ok = True
    for name, module in test_imports:
        try:
            __import__(module)
            print(f"✅ {name}: 导入成功")
        except ImportError as e:
            print(f"❌ {name}: 导入失败 - {e}")
            all_ok = False
    
    return all_ok

def main():
    """主函数"""
    if install_dependencies():
        if verify_installation():
            print("\n🎉 安装验证成功!")
            print("💡 现在可以运行系统:")
            print("   python run.py")
            print("   或")
            print("   python test_system.py  # 运行测试")
        else:
            print("\n⚠️ 安装验证失败，请检查错误信息")
    else:
        print("\n❌ 安装失败")

if __name__ == "__main__":
    main()
