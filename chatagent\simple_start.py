#!/usr/bin/env python3
"""
智能聊天系统简单启动脚本
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def main():
    """主函数"""
    print("🤖 智能聊天系统")
    print("基于AutoGen + DeepSeek + FastAPI")
    print("=" * 40)
    
    # 检查项目结构
    backend_dir = Path(__file__).parent / "backend"
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return
    
    print("✅ 项目结构检查通过")
    
    # 切换到backend目录
    original_dir = os.getcwd()
    os.chdir(backend_dir)
    print(f"📁 工作目录: {backend_dir}")
    
    try:
        # 启动服务器
        print("🚀 启动FastAPI服务器...")
        
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        print("⏳ 正在启动服务器...")
        
        # 启动服务器
        process = subprocess.Popen(cmd)
        
        # 等待服务器启动
        time.sleep(5)
        
        print("✅ 服务器已启动!")
        print("🌐 访问地址:")
        print("   - 聊天界面: http://localhost:8000/static/index.html")
        print("   - API文档: http://localhost:8000/docs")
        print("   - 系统状态: http://localhost:8000/api/health")
        
        # 自动打开浏览器
        try:
            webbrowser.open("http://localhost:8000/static/index.html")
            print("🌐 已自动打开浏览器")
        except:
            print("⚠️ 无法自动打开浏览器，请手动访问上述地址")
        
        print("\n" + "="*50)
        print("🎉 智能聊天系统运行中!")
        print("💡 基于AutoGen + DeepSeek")
        print("🔄 支持流式对话输出")
        print("🎨 Gemini风格界面设计")
        print("="*50)
        print("\n按 Ctrl+C 停止服务器")
        
        # 等待用户中断
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务器...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            print("✅ 服务器已停止")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请确保已安装依赖: pip install -r requirements.txt")
    
    finally:
        # 恢复原始工作目录
        os.chdir(original_dir)
    
    print("👋 感谢使用智能聊天系统!")

if __name__ == "__main__":
    main()
