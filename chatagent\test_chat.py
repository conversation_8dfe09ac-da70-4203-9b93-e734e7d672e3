#!/usr/bin/env python3
"""
测试聊天功能
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加backend目录到路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

async def test_chat():
    """测试聊天功能"""
    print("🧪 测试聊天功能")
    print("=" * 40)
    
    try:
        # 导入聊天服务
        from chat_service import ChatService
        
        # 初始化聊天服务
        print("🚀 初始化聊天服务...")
        chat_service = ChatService()
        
        if not chat_service.is_initialized:
            print("❌ 聊天服务初始化失败")
            return
        
        print("✅ 聊天服务初始化成功")
        
        # 测试对话
        test_messages = [
            "你好",
            "你是谁？",
            "请介绍一下你的功能",
            "谢谢"
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n📝 测试消息 {i}: {message}")
            print("🤖 AI回复:")
            
            # 测试流式对话
            response_parts = []
            async for chunk in chat_service.chat_stream(message):
                try:
                    import json
                    data = json.loads(chunk)
                    if data.get("type") == "chunk":
                        content = data.get("data", {}).get("content", "")
                        response_parts.append(content)
                        print(content, end="", flush=True)
                    elif data.get("type") == "complete":
                        full_response = data.get("data", {}).get("full_response", "")
                        if full_response and not response_parts:
                            print(full_response)
                        break
                    elif data.get("type") == "error":
                        error_msg = data.get("data", {}).get("message", "未知错误")
                        print(f"❌ 错误: {error_msg}")
                        break
                except json.JSONDecodeError:
                    print(f"⚠️ 解析响应失败: {chunk}")
            
            print("\n" + "-" * 40)
        
        print("\n✅ 聊天功能测试完成")
        
        # 显示对话历史
        history = chat_service.get_conversation_history()
        print(f"📚 对话历史记录: {len(history)} 条消息")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

def main():
    """主函数"""
    print("🤖 智能聊天系统 - 聊天功能测试")
    print("基于AutoGen + DeepSeek")
    print("=" * 50)
    
    # 运行异步测试
    asyncio.run(test_chat())
    
    print("\n👋 测试完成!")

if __name__ == "__main__":
    main()
