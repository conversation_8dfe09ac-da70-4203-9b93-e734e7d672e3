<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天系统测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1557b0;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🤖 智能聊天系统测试页面</h1>
    
    <div class="test-section">
        <h2>1. 系统状态检查</h2>
        <button onclick="checkHealth()">检查系统健康状态</button>
        <button onclick="checkStatus()">检查系统状态</button>
        <div id="healthStatus" class="status info">等待检查...</div>
    </div>
    
    <div class="test-section">
        <h2>2. 会话管理测试</h2>
        <button onclick="createSession()">创建新会话</button>
        <button onclick="getSessionInfo()">获取会话信息</button>
        <button onclick="deleteSession()">删除会话</button>
        <div>当前会话ID: <span id="currentSessionId">无</span></div>
        <div id="sessionStatus" class="status info">等待操作...</div>
    </div>
    
    <div class="test-section">
        <h2>3. 聊天功能测试</h2>
        <input type="text" id="messageInput" placeholder="输入测试消息..." value="你好，这是一个测试消息">
        <button onclick="sendMessage()">发送消息</button>
        <button onclick="clearChat()">清空对话</button>
        <div id="chatStatus" class="status info">等待发送...</div>
        <div id="chatResponse" style="background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 4px; min-height: 50px;"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 文件上传测试</h2>
        <input type="file" id="fileInput" accept=".txt,.json,.md">
        <button onclick="uploadFile()">上传文件</button>
        <div id="uploadStatus" class="status info">等待上传...</div>
    </div>
    
    <div class="test-section">
        <h2>5. 调试日志</h2>
        <button onclick="clearLog()">清空日志</button>
        <div id="log"></div>
    </div>

    <script>
        let currentSessionId = null;
        const apiBase = window.location.origin;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function setStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function checkHealth() {
            try {
                log('🔍 检查系统健康状态...');
                const response = await fetch(`${apiBase}/api/health`);
                const data = await response.json();
                
                if (response.ok) {
                    log(`✅ 系统健康检查成功: ${JSON.stringify(data)}`);
                    setStatus('healthStatus', `系统状态: ${data.status}`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ 系统健康检查失败: ${error.message}`);
                setStatus('healthStatus', `检查失败: ${error.message}`, 'error');
            }
        }
        
        async function checkStatus() {
            try {
                log('🔍 检查系统详细状态...');
                const response = await fetch(`${apiBase}/api/status`);
                const data = await response.json();
                
                if (response.ok) {
                    log(`✅ 系统状态检查成功: ${JSON.stringify(data, null, 2)}`);
                    setStatus('healthStatus', '系统运行正常', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ 系统状态检查失败: ${error.message}`);
                setStatus('healthStatus', `检查失败: ${error.message}`, 'error');
            }
        }
        
        async function createSession() {
            try {
                log('🆕 创建新会话...');
                const response = await fetch(`${apiBase}/api/session/create`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentSessionId = data.session_id;
                    document.getElementById('currentSessionId').textContent = currentSessionId;
                    log(`✅ 会话创建成功: ${currentSessionId}`);
                    setStatus('sessionStatus', '会话创建成功', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ 会话创建失败: ${error.message}`);
                setStatus('sessionStatus', `创建失败: ${error.message}`, 'error');
            }
        }
        
        async function getSessionInfo() {
            if (!currentSessionId) {
                setStatus('sessionStatus', '请先创建会话', 'error');
                return;
            }
            
            try {
                log(`🔍 获取会话信息: ${currentSessionId}`);
                const response = await fetch(`${apiBase}/api/session/${currentSessionId}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 会话信息: ${JSON.stringify(data, null, 2)}`);
                    setStatus('sessionStatus', '会话信息获取成功', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ 获取会话信息失败: ${error.message}`);
                setStatus('sessionStatus', `获取失败: ${error.message}`, 'error');
            }
        }
        
        async function deleteSession() {
            if (!currentSessionId) {
                setStatus('sessionStatus', '没有可删除的会话', 'error');
                return;
            }
            
            try {
                log(`🗑️ 删除会话: ${currentSessionId}`);
                const response = await fetch(`${apiBase}/api/session/${currentSessionId}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    log(`✅ 会话删除成功: ${currentSessionId}`);
                    currentSessionId = null;
                    document.getElementById('currentSessionId').textContent = '无';
                    setStatus('sessionStatus', '会话删除成功', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ 会话删除失败: ${error.message}`);
                setStatus('sessionStatus', `删除失败: ${error.message}`, 'error');
            }
        }
        
        async function sendMessage() {
            const message = document.getElementById('messageInput').value.trim();
            if (!message) {
                setStatus('chatStatus', '请输入消息', 'error');
                return;
            }
            
            if (!currentSessionId) {
                await createSession();
                if (!currentSessionId) {
                    setStatus('chatStatus', '无法创建会话', 'error');
                    return;
                }
            }
            
            try {
                log(`💬 发送消息: ${message}`);
                setStatus('chatStatus', '正在发送...', 'info');
                
                const response = await fetch(`${apiBase}/api/chat`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message,
                        session_id: currentSessionId
                    })
                });
                
                if (response.ok) {
                    log(`✅ 消息发送成功，开始接收回复...`);
                    setStatus('chatStatus', '接收回复中...', 'info');
                    
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let aiResponse = '';
                    
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;
                        
                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.slice(6);
                                if (data.trim()) {
                                    try {
                                        const parsed = JSON.parse(data);
                                        if (parsed.type === 'chunk') {
                                            aiResponse += parsed.data.content || '';
                                            document.getElementById('chatResponse').textContent = aiResponse;
                                        } else if (parsed.type === 'complete') {
                                            aiResponse = parsed.data.full_response || aiResponse;
                                            document.getElementById('chatResponse').textContent = aiResponse;
                                            log(`✅ AI回复完成: ${aiResponse.substring(0, 50)}...`);
                                            setStatus('chatStatus', 'AI回复完成', 'success');
                                            return;
                                        } else if (parsed.type === 'error') {
                                            throw new Error(parsed.data.message);
                                        }
                                    } catch (e) {
                                        console.warn('解析SSE数据失败:', e, data);
                                    }
                                }
                            }
                        }
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ 发送消息失败: ${error.message}`);
                setStatus('chatStatus', `发送失败: ${error.message}`, 'error');
            }
        }
        
        async function clearChat() {
            try {
                log('🧹 清空对话历史...');
                const response = await fetch(`${apiBase}/api/clear`, { method: 'POST' });
                
                if (response.ok) {
                    log('✅ 对话历史清空成功');
                    setStatus('chatStatus', '对话历史已清空', 'success');
                    document.getElementById('chatResponse').textContent = '';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ 清空对话失败: ${error.message}`);
                setStatus('chatStatus', `清空失败: ${error.message}`, 'error');
            }
        }
        
        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                setStatus('uploadStatus', '请选择文件', 'error');
                return;
            }
            
            if (!currentSessionId) {
                await createSession();
                if (!currentSessionId) {
                    setStatus('uploadStatus', '无法创建会话', 'error');
                    return;
                }
            }
            
            try {
                log(`📁 上传文件: ${file.name}`);
                setStatus('uploadStatus', '正在上传...', 'info');
                
                const formData = new FormData();
                formData.append('session_id', currentSessionId);
                formData.append('file', file);
                
                const response = await fetch(`${apiBase}/api/upload`, {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 文件上传成功: ${JSON.stringify(data)}`);
                    setStatus('uploadStatus', '文件上传成功', 'success');
                } else {
                    const error = await response.json();
                    throw new Error(error.detail || `HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ 文件上传失败: ${error.message}`);
                setStatus('uploadStatus', `上传失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动检查系统状态
        window.onload = function() {
            log('🚀 测试页面加载完成');
            checkHealth();
        };
    </script>
</body>
</html>
