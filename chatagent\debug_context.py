#!/usr/bin/env python3
"""
调试上下文传递的脚本
"""

import requests
import json

API_BASE = "http://localhost:8000"

def debug_context():
    """调试上下文传递"""
    print("🔍 调试上下文传递...")
    
    # 创建会话
    response = requests.post(f"{API_BASE}/api/session/create", json={})
    session_data = response.json()
    session_id = session_data["session_id"]
    print(f"✅ 会话创建: {session_id}")
    
    # 第一条消息
    print(f"\n1. 发送第一条消息...")
    response = requests.post(
        f"{API_BASE}/api/chat",
        json={
            "message": "我叫张三",
            "session_id": session_id
        },
        stream=True
    )
    
    # 处理响应
    for line in response.iter_lines():
        if line:
            line = line.decode('utf-8')
            if line.startswith("data: "):
                data = line[6:]
                if data.strip():
                    try:
                        parsed = json.loads(data)
                        if parsed["type"] == "complete":
                            print(f"AI回复: {parsed['data']['full_response'][:100]}...")
                            break
                    except:
                        pass
    
    # 检查会话历史
    print(f"\n2. 检查会话历史...")
    response = requests.get(f"{API_BASE}/api/session/{session_id}/messages")
    if response.status_code == 200:
        data = response.json()
        messages = data.get("messages", [])
        print(f"会话中有 {len(messages)} 条消息:")
        for i, msg in enumerate(messages):
            if isinstance(msg, dict):
                print(f"  {i+1}. {msg['role']}: {msg['content'][:50]}...")
            else:
                print(f"  {i+1}. 消息格式错误: {msg}")
    else:
        print(f"获取会话历史失败: {response.status_code}")
    
    # 第二条消息
    print(f"\n3. 发送第二条消息...")
    response = requests.post(
        f"{API_BASE}/api/chat",
        json={
            "message": "我的名字是什么？",
            "session_id": session_id
        },
        stream=True
    )
    
    # 处理响应
    ai_response = ""
    for line in response.iter_lines():
        if line:
            line = line.decode('utf-8')
            if line.startswith("data: "):
                data = line[6:]
                if data.strip():
                    try:
                        parsed = json.loads(data)
                        if parsed["type"] == "complete":
                            ai_response = parsed['data']['full_response']
                            break
                    except:
                        pass
    
    print(f"AI回复: {ai_response}")
    
    # 检查是否包含"张三"
    if "张三" in ai_response:
        print("✅ 上下文记忆正常！AI记住了名字")
    else:
        print("❌ 上下文记忆失败！AI没有记住名字")
    
    # 再次检查会话历史
    print(f"\n4. 最终会话历史...")
    response = requests.get(f"{API_BASE}/api/session/{session_id}/messages")
    if response.status_code == 200:
        data = response.json()
        messages = data.get("messages", [])
        print(f"会话中有 {len(messages)} 条消息:")
        for i, msg in enumerate(messages):
            if isinstance(msg, dict):
                print(f"  {i+1}. {msg['role']}: {msg['content'][:50]}...")
            else:
                print(f"  {i+1}. 消息格式错误: {msg}")
    else:
        print(f"获取会话历史失败: {response.status_code}")

if __name__ == "__main__":
    debug_context()
