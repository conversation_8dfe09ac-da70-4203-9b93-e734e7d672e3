# Copyright (c) 2023 - 2025, AG2ai, Inc., AG2ai open-source projects maintainers and core contributors
#
# SPDX-License-Identifier: Apache-2.0
def detect_outlier_zscore(csv_file, column_name, threshold=3):
    """Detect outliers in a CSV file based on a specified column. The outliers are determined by calculating the z-score of the data points in the column.

    Args:
    csv_file (str): The path to the CSV file.
    column_name (str): The name of the column to calculate z-scores for.
    threshold (float, optional): The threshold value for determining outliers. By default set to 3.

    Returns:
    list: A list of row indices where the z-score is above the threshold.
    """
    import numpy as np
    import pandas as pd

    # Read the CSV file into a pandas DataFrame
    df = pd.read_csv(csv_file)

    # Calculate the z-score for the specified column
    z_scores = np.abs((df[column_name] - df[column_name].mean()) / df[column_name].std())

    # Find the row indices where the z-score is above the threshold
    outlier_indices = np.where(z_scores > threshold)[0]

    # Return the row indices of the outliers
    return outlier_indices
