#!/usr/bin/env python3
"""
详细的上下文调试脚本
直接调用后端模块来测试上下文传递
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from session_manager import SessionManager
from chat_service import ChatService
import asyncio

async def test_context_directly():
    """直接测试后端上下文功能"""
    print("🔍 直接测试后端上下文功能...")

    # 使用全局session_manager实例
    from session_manager import session_manager
    chat_service = ChatService()
    
    # 创建会话
    session_id = session_manager.create_session()
    print(f"✅ 会话创建: {session_id}")
    
    # 第一条消息
    print("\n--- 第一轮对话 ---")
    print("👤 用户: 我叫张三，今年25岁")

    # 获取AI回复（不要手动添加消息，让chat_stream自己处理）
    ai_response1 = ""
    async for chunk in chat_service.chat_stream("我叫张三，今年25岁", session_id):
        if '"type": "complete"' in chunk:
            import json
            try:
                data = json.loads(chunk)
                ai_response1 = data["data"]["full_response"]
                break
            except:
                pass
    
    print(f"🤖 AI: {ai_response1}")
    
    # 检查会话历史
    print("\n📊 检查会话历史:")
    context = session_manager.get_conversation_context(session_id)
    for i, msg in enumerate(context):
        print(f"  {i+1}. {msg['role']}: {msg['content'][:50]}...")
    
    # 第二条消息
    print("\n--- 第二轮对话 ---")
    print("👤 用户: 我的名字是什么？")
    
    # 获取AI回复
    ai_response2 = ""
    async for chunk in chat_service.chat_stream("我的名字是什么？", session_id):
        if '"type": "complete"' in chunk:
            import json
            try:
                data = json.loads(chunk)
                ai_response2 = data["data"]["full_response"]
                break
            except:
                pass
    
    print(f"🤖 AI: {ai_response2}")
    
    # 检查上下文记忆
    if "张三" in ai_response2:
        print("✅ 上下文记忆正常！AI记住了名字")
    else:
        print("❌ 上下文记忆失败！AI没有记住名字")
    
    # 最终会话历史
    print("\n📊 最终会话历史:")
    context = session_manager.get_conversation_context(session_id)
    for i, msg in enumerate(context):
        print(f"  {i+1}. {msg['role']}: {msg['content'][:50]}...")

def test_session_manager():
    """测试会话管理器的上下文功能"""
    print("🔍 测试会话管理器...")
    
    session_manager = SessionManager()
    
    # 创建会话
    session_id = session_manager.create_session()
    print(f"✅ 会话创建: {session_id}")
    
    # 添加消息
    session_manager.add_message(session_id, "user", "我叫李四")
    session_manager.add_message(session_id, "assistant", "你好李四！")
    session_manager.add_message(session_id, "user", "我的名字是什么？")
    
    # 获取上下文
    context = session_manager.get_conversation_context(session_id)
    print(f"📊 上下文包含 {len(context)} 条消息:")
    for i, msg in enumerate(context):
        print(f"  {i+1}. {msg['role']}: {msg['content']}")
    
    # 测试排除最后一条消息
    context_without_last = context[:-1]
    print(f"\n📊 排除最后一条消息后有 {len(context_without_last)} 条:")
    for i, msg in enumerate(context_without_last):
        print(f"  {i+1}. {msg['role']}: {msg['content']}")

async def test_deepseek_api_directly():
    """直接测试DeepSeek API调用"""
    print("🔍 直接测试DeepSeek API...")
    
    import httpx
    import json
    from config import DEEPSEEK_API_KEY, DEEPSEEK_BASE_URL
    
    headers = {
        "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
        "Content-Type": "application/json"
    }
    
    # 构建测试消息
    messages = [
        {
            "role": "system",
            "content": "你是一个AI助手，具有强大的上下文记忆能力。请仔细阅读对话历史，记住用户提到的信息。"
        },
        {
            "role": "user",
            "content": "我叫王五，是程序员"
        },
        {
            "role": "assistant", 
            "content": "你好王五！很高兴认识你这位程序员朋友。"
        },
        {
            "role": "user",
            "content": "我的职业是什么？"
        }
    ]
    
    print("📤 发送给DeepSeek的消息:")
    for i, msg in enumerate(messages):
        print(f"  {i+1}. {msg['role']}: {msg['content']}")
    
    data = {
        "model": "deepseek-chat",
        "messages": messages,
        "temperature": 0.7,
        "max_tokens": 1000,
        "stream": False
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{DEEPSEEK_BASE_URL}/chat/completions",
                headers=headers,
                json=data
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result['choices'][0]['message']['content']
                print(f"📥 DeepSeek回复: {ai_response}")
                
                if "程序员" in ai_response or "王五" in ai_response:
                    print("✅ DeepSeek API上下文记忆正常！")
                else:
                    print("❌ DeepSeek API上下文记忆失败！")
            else:
                print(f"❌ API调用失败: {response.status_code} - {response.text}")
                
    except Exception as e:
        print(f"❌ API调用异常: {e}")

async def main():
    """主测试函数"""
    print("🚀 开始详细的上下文调试...")
    print("=" * 60)
    
    # 测试1：会话管理器
    print("\n1. 测试会话管理器")
    test_session_manager()
    
    # 测试2：直接API调用
    print("\n2. 测试DeepSeek API")
    await test_deepseek_api_directly()
    
    # 测试3：完整流程
    print("\n3. 测试完整聊天流程")
    await test_context_directly()
    
    print("\n" + "=" * 60)
    print("🎉 调试完成！")

if __name__ == "__main__":
    asyncio.run(main())
