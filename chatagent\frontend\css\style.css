/* 智能聊天系统 - Gemini风格样式 */

/* 全局变量 */
:root {
    /* Gemini配色方案 */
    --primary-color: #1a73e8;
    --primary-hover: #1557b0;
    --secondary-color: #34a853;
    --accent-color: #fbbc04;
    --error-color: #ea4335;
    
    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e8f0fe;
    --bg-chat: #fafafa;
    
    /* 文字颜色 */
    --text-primary: #202124;
    --text-secondary: #5f6368;
    --text-tertiary: #80868b;
    --text-inverse: #ffffff;
    
    /* 边框和阴影 */
    --border-color: #dadce0;
    --border-radius: 12px;
    --shadow-light: 0 1px 3px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 12px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 24px rgba(0,0,0,0.2);
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* 字体 */
    --font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-sm: 12px;
    --font-size-base: 14px;
    --font-size-lg: 16px;
    --font-size-xl: 18px;
    --font-size-xxl: 24px;
}

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    background: var(--bg-primary);
    line-height: 1.5;
    overflow: hidden;
}

/* 主容器 */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 头部样式 */
.app-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-light);
    z-index: 100;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
}

.app-title {
    font-size: var(--font-size-xl);
    font-weight: 500;
    color: var(--text-primary);
}

.app-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background: var(--bg-tertiary);
    padding: 2px 8px;
    border-radius: 12px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.session-info {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: 20px;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.action-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-secondary);
}

.action-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 20px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--secondary-color);
    animation: pulse 2s infinite;
}

.status-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 聊天容器 */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
    background: var(--bg-primary);
    border-radius: var(--spacing-lg) var(--spacing-lg) 0 0;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
    background: var(--bg-chat);
}

/* 欢迎消息 */
.welcome-message {
    text-align: center;
    padding: var(--spacing-xl);
    max-width: 500px;
    margin: 0 auto;
}

.welcome-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-lg);
}

.welcome-message h2 {
    font-size: var(--font-size-xxl);
    font-weight: 500;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.welcome-message p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.feature-tags {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
    flex-wrap: wrap;
}

.feature-tag {
    background: var(--bg-tertiary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 20px;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* 消息样式 */
.message {
    margin-bottom: var(--spacing-lg);
    display: flex;
    gap: var(--spacing-md);
    animation: fadeInUp 0.3s ease;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.message.assistant .message-avatar {
    background: var(--secondary-color);
    color: var(--text-inverse);
}

.message-content {
    max-width: 70%;
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    position: relative;
}

.message.user .message-content {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.message-text {
    line-height: 1.6;
    word-wrap: break-word;
}

.message-time {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    margin-top: var(--spacing-sm);
}

.message.user .message-time {
    color: rgba(255, 255, 255, 0.7);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 输入区域 */
.input-container {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-lg);
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    border: 2px solid transparent;
    transition: all 0.2s ease;
}

.file-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--text-tertiary);
    color: var(--text-inverse);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.file-btn:hover {
    background: var(--text-secondary);
    transform: scale(1.05);
}

.file-upload-area {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 2px dashed var(--border-color);
}

.uploaded-files {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.file-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-primary);
    border-radius: 20px;
    border: 1px solid var(--border-color);
    font-size: var(--font-size-sm);
}

.file-item .file-icon {
    width: 16px;
    height: 16px;
    color: var(--primary-color);
}

.file-item .file-name {
    color: var(--text-primary);
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-item .file-size {
    color: var(--text-tertiary);
}

.file-item .remove-file {
    width: 16px;
    height: 16px;
    border: none;
    background: transparent;
    color: var(--error-color);
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.file-item .remove-file:hover {
    background: var(--error-color);
    color: var(--text-inverse);
}

.input-wrapper:focus-within {
    border-color: var(--primary-color);
    background: var(--bg-primary);
}

#messageInput {
    flex: 1;
    border: none;
    background: transparent;
    resize: none;
    outline: none;
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    line-height: 1.5;
    max-height: 120px;
}

#messageInput::placeholder {
    color: var(--text-tertiary);
}

.send-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--primary-color);
    color: var(--text-inverse);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: scale(1.05);
}

.send-btn:disabled {
    background: var(--text-tertiary);
    cursor: not-allowed;
    transform: none;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--bg-secondary);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 设置面板 */
.settings-panel {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: var(--bg-primary);
    box-shadow: var(--shadow-heavy);
    transition: right 0.3s ease;
    z-index: 1001;
}

.settings-panel.show {
    right: 0;
}

.settings-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.settings-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.settings-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 500;
}

.close-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-secondary);
}

.close-btn:hover {
    background: var(--bg-secondary);
}

.settings-body {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.setting-item {
    margin-bottom: var(--spacing-lg);
}

.setting-item label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.setting-item select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--spacing-sm);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-family);
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--text-tertiary);
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.settings-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.system-info p {
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-container {
        height: 100vh;
    }
    
    .header-content {
        padding: var(--spacing-md);
    }
    
    .app-title {
        font-size: var(--font-size-lg);
    }
    
    .chat-container {
        border-radius: 0;
        margin: 0;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .settings-panel {
        width: 100%;
        right: -100%;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--text-tertiary);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* ==================== 主题切换样式 ==================== */

/* 深色主题 */
.theme-dark {
    --bg-primary: #1f1f1f;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3c4043;
    --bg-chat: #202124;

    --text-primary: #e8eaed;
    --text-secondary: #9aa0a6;
    --text-tertiary: #5f6368;
    --text-inverse: #202124;

    --border-color: #3c4043;
}

.theme-dark .app-container {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
}

.theme-dark .message.user .message-content {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.theme-dark .loading-overlay {
    background: rgba(32, 33, 36, 0.9);
}

/* 高对比度主题 */
.theme-high-contrast {
    --bg-primary: #000000;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #333333;
    --bg-chat: #0d0d0d;

    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-tertiary: #999999;
    --text-inverse: #000000;

    --border-color: #666666;
    --primary-color: #00ff00;
    --secondary-color: #ffff00;
    --error-color: #ff0000;
}

.theme-high-contrast .app-container {
    background: #000000;
}

/* ==================== 字体大小切换 ==================== */

/* 小字体 */
.font-small {
    --font-size-sm: 10px;
    --font-size-base: 12px;
    --font-size-lg: 14px;
    --font-size-xl: 16px;
    --font-size-xxl: 20px;
}

/* 中等字体（默认） */
.font-medium {
    --font-size-sm: 12px;
    --font-size-base: 14px;
    --font-size-lg: 16px;
    --font-size-xl: 18px;
    --font-size-xxl: 24px;
}

/* 大字体 */
.font-large {
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-xxl: 28px;
}

/* 超大字体 */
.font-extra-large {
    --font-size-sm: 16px;
    --font-size-base: 18px;
    --font-size-lg: 20px;
    --font-size-xl: 24px;
    --font-size-xxl: 32px;
}

/* ==================== 主题过渡动画 ==================== */

body, .app-container, .chat-container, .message-content, .input-wrapper {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* ==================== 打印样式 ==================== */

@media print {
    .app-header, .input-container, .settings-panel {
        display: none !important;
    }

    .chat-container {
        height: auto !important;
        box-shadow: none !important;
        border-radius: 0 !important;
    }

    .chat-messages {
        overflow: visible !important;
        height: auto !important;
    }

    .message {
        break-inside: avoid;
    }
}
