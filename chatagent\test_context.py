#!/usr/bin/env python3
"""
上下文记忆功能测试脚本
测试多轮对话中的上下文记忆能力
"""

import asyncio
import json
import httpx
import time

API_BASE = "http://localhost:8000"

async def test_context_memory():
    """测试上下文记忆功能"""
    print("🧪 开始测试上下文记忆功能...")
    
    # 创建会话
    print("\n1. 创建新会话...")
    async with httpx.AsyncClient() as client:
        response = await client.post(f"{API_BASE}/api/session/create")
        if response.status_code == 200:
            session_data = response.json()
            session_id = session_data["session_id"]
            print(f"✅ 会话创建成功: {session_id}")
        else:
            print(f"❌ 会话创建失败: {response.status_code}")
            return
    
    # 测试对话序列
    test_conversations = [
        {
            "message": "我叫张三，今年25岁，是一名软件工程师",
            "expected_context": ["姓名", "年龄", "职业"]
        },
        {
            "message": "我喜欢编程和阅读，特别是Python和机器学习",
            "expected_context": ["爱好", "技术栈"]
        },
        {
            "message": "请问你还记得我的名字吗？",
            "expected_context": ["张三"]
        },
        {
            "message": "我的年龄是多少？",
            "expected_context": ["25岁"]
        },
        {
            "message": "我的职业是什么？",
            "expected_context": ["软件工程师"]
        },
        {
            "message": "根据我之前说的信息，推荐一些适合我的学习资源",
            "expected_context": ["Python", "机器学习", "软件工程师"]
        }
    ]
    
    print(f"\n2. 开始多轮对话测试（共{len(test_conversations)}轮）...")
    
    for i, test_case in enumerate(test_conversations, 1):
        print(f"\n--- 第{i}轮对话 ---")
        print(f"👤 用户: {test_case['message']}")
        
        # 发送消息
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{API_BASE}/api/chat",
                json={
                    "message": test_case["message"],
                    "session_id": session_id
                }
            )
            
            if response.status_code == 200:
                # 处理流式响应
                ai_response = ""
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]
                        if data.strip():
                            try:
                                parsed = json.loads(data)
                                if parsed["type"] == "chunk":
                                    ai_response += parsed["data"].get("content", "")
                                elif parsed["type"] == "complete":
                                    ai_response = parsed["data"].get("full_response", ai_response)
                                    break
                                elif parsed["type"] == "error":
                                    print(f"❌ AI回复错误: {parsed['data']['message']}")
                                    break
                            except json.JSONDecodeError:
                                continue
                
                print(f"🤖 AI: {ai_response}")
                
                # 检查上下文记忆
                context_found = []
                for expected in test_case["expected_context"]:
                    if expected.lower() in ai_response.lower():
                        context_found.append(expected)
                
                if context_found:
                    print(f"✅ 上下文记忆正常，识别到: {', '.join(context_found)}")
                else:
                    print(f"⚠️ 上下文记忆可能有问题，期望识别: {', '.join(test_case['expected_context'])}")
                
            else:
                print(f"❌ 请求失败: {response.status_code}")
        
        # 等待一下再进行下一轮
        await asyncio.sleep(1)
    
    # 获取会话信息验证
    print(f"\n3. 验证会话历史...")
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{API_BASE}/api/session/{session_id}/messages")
        if response.status_code == 200:
            messages = response.json()
            print(f"✅ 会话历史包含 {len(messages)} 条消息")
            
            # 显示消息历史
            for msg in messages:
                role = "👤" if msg["role"] == "user" else "🤖"
                content = msg["content"][:50] + "..." if len(msg["content"]) > 50 else msg["content"]
                print(f"  {role} {content}")
        else:
            print(f"❌ 获取会话历史失败: {response.status_code}")
    
    print(f"\n🎉 上下文记忆测试完成！")

async def test_session_isolation():
    """测试会话隔离功能"""
    print("\n🔒 测试会话隔离功能...")
    
    # 创建两个会话
    sessions = []
    for i in range(2):
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE}/api/session/create")
            if response.status_code == 200:
                session_data = response.json()
                sessions.append(session_data["session_id"])
                print(f"✅ 会话{i+1}创建成功: {session_data['session_id']}")
    
    if len(sessions) != 2:
        print("❌ 会话创建失败，无法测试隔离功能")
        return
    
    # 在第一个会话中说话
    print(f"\n在会话1中说话...")
    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.post(
            f"{API_BASE}/api/chat",
            json={
                "message": "我叫李四，是一名医生",
                "session_id": sessions[0]
            }
        )
        print("✅ 会话1消息发送完成")
    
    # 在第二个会话中询问
    print(f"\n在会话2中询问...")
    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.post(
            f"{API_BASE}/api/chat",
            json={
                "message": "你知道我的名字吗？",
                "session_id": sessions[1]
            }
        )
        
        ai_response = ""
        async for line in response.aiter_lines():
            if line.startswith("data: "):
                data = line[6:]
                if data.strip():
                    try:
                        parsed = json.loads(data)
                        if parsed["type"] == "complete":
                            ai_response = parsed["data"].get("full_response", "")
                            break
                    except json.JSONDecodeError:
                        continue
        
        print(f"🤖 会话2的AI回复: {ai_response}")
        
        # 检查是否泄露了会话1的信息
        if "李四" in ai_response or "医生" in ai_response:
            print("❌ 会话隔离失败！会话2能访问会话1的信息")
        else:
            print("✅ 会话隔离正常！会话2无法访问会话1的信息")

async def main():
    """主测试函数"""
    print("🚀 开始上下文和会话管理测试")
    print("=" * 50)
    
    try:
        # 检查服务器状态
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{API_BASE}/api/health")
            if response.status_code == 200:
                print("✅ 服务器连接正常")
            else:
                print("❌ 服务器连接失败")
                return
        
        # 测试上下文记忆
        await test_context_memory()
        
        # 测试会话隔离
        await test_session_isolation()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
