/**
 * 智能聊天系统 - 前端JavaScript主程序
 *
 * 主要功能：
 * 1. 管理聊天界面和用户交互
 * 2. 处理SSE流式通信
 * 3. 文件上传和管理
 * 4. 会话管理和状态维护
 * 5. 设置管理和主题切换
 *
 * 技术特点：
 * - 原生JavaScript实现，无框架依赖
 * - EventSource处理SSE流式数据
 * - FormData处理文件上传
 * - localStorage存储用户设置
 */

class ChatApp {
    /**
     * 聊天应用主类构造函数
     * 初始化应用状态、DOM元素绑定、系统检查等
     */
    constructor() {
        // 后端API基础地址
        this.apiBase = window.location.origin;
        // 系统连接状态标志
        this.isConnected = false;
        // 流式传输状态标志
        this.isStreaming = false;
        // 当前EventSource连接对象
        this.currentEventSource = null;
        // 当前会话ID
        this.sessionId = null;
        // 已上传文件的存储Map：{fileId: fileInfo}
        this.uploadedFiles = new Map();

        // 执行初始化流程
        this.initializeElements();  // 获取DOM元素引用
        this.bindEvents();  // 绑定事件监听器
        this.checkSystemStatus();  // 检查系统状态
        this.loadSettings();  // 加载用户设置
        this.createSession();  // 创建新的聊天会话
    }
    
    initializeElements() {
        // 获取DOM元素
        this.elements = {
            messageInput: document.getElementById('messageInput'),
            sendBtn: document.getElementById('sendBtn'),
            chatMessages: document.getElementById('chatMessages'),
            clearBtn: document.getElementById('clearBtn'),
            newSessionBtn: document.getElementById('newSessionBtn'),
            settingsBtn: document.getElementById('settingsBtn'),
            settingsPanel: document.getElementById('settingsPanel'),
            closeSettingsBtn: document.getElementById('closeSettingsBtn'),
            statusIndicator: document.getElementById('statusIndicator'),
            statusText: document.querySelector('.status-text'),
            loadingOverlay: document.getElementById('loadingOverlay'),
            charCount: document.getElementById('charCount'),
            systemStatus: document.getElementById('systemStatus'),
            themeSelect: document.getElementById('themeSelect'),
            fontSizeSelect: document.getElementById('fontSizeSelect'),
            streamToggle: document.getElementById('streamToggle'),
            sessionInfo: document.getElementById('sessionInfo'),
            sessionId: document.getElementById('sessionId'),
            fileBtn: document.getElementById('fileBtn'),
            fileInput: document.getElementById('fileInput'),
            fileUploadArea: document.getElementById('fileUploadArea'),
            uploadedFiles: document.getElementById('uploadedFiles')
        };
    }
    
    bindEvents() {
        // 发送按钮点击
        this.elements.sendBtn.addEventListener('click', () => this.sendMessage());
        
        // 输入框事件
        this.elements.messageInput.addEventListener('input', (e) => {
            this.updateCharCount();
            this.updateSendButton();
            this.autoResize(e.target);
        });
        
        this.elements.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // 清空对话
        this.elements.clearBtn.addEventListener('click', () => this.clearChat());

        // 新建会话
        this.elements.newSessionBtn.addEventListener('click', () => this.createSession());

        // 文件上传
        this.elements.fileBtn.addEventListener('click', () => this.elements.fileInput.click());
        this.elements.fileInput.addEventListener('change', (e) => this.handleFileUpload(e));

        // 设置面板
        this.elements.settingsBtn.addEventListener('click', () => this.showSettings());
        this.elements.closeSettingsBtn.addEventListener('click', () => this.hideSettings());
        
        // 设置变更
        this.elements.themeSelect.addEventListener('change', () => this.applySettings());
        this.elements.fontSizeSelect.addEventListener('change', () => this.applySettings());
        this.elements.streamToggle.addEventListener('change', () => this.applySettings());
        
        // 点击外部关闭设置面板
        document.addEventListener('click', (e) => {
            if (!this.elements.settingsPanel.contains(e.target) && 
                !this.elements.settingsBtn.contains(e.target)) {
                this.hideSettings();
            }
        });
    }
    
    async createSession() {
        """创建新会话"""
        try {
            const response = await fetch(`${this.apiBase}/api/session/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({})
            });

            if (response.ok) {
                const data = await response.json();
                this.sessionId = data.session_id;
                this.updateSessionDisplay();
                this.clearChatMessages();
                console.log('✅ 新会话创建成功:', this.sessionId);
            } else {
                console.error('❌ 创建会话失败');
            }
        } catch (error) {
            console.error('❌ 创建会话异常:', error);
        }
    }

    updateSessionDisplay() {
        """更新会话显示"""
        if (this.sessionId && this.elements.sessionId) {
            const shortId = this.sessionId.substring(0, 8) + '...';
            this.elements.sessionId.textContent = `会话: ${shortId}`;
        }
    }

    clearChatMessages() {
        """清空聊天消息显示"""
        this.elements.chatMessages.innerHTML = `
            <div class="welcome-message">
                <div class="welcome-icon">
                    <span class="material-icons">waving_hand</span>
                </div>
                <h2>欢迎使用智能聊天系统</h2>
                <p>我是基于AutoGen + DeepSeek的AI助手，可以帮助您解答问题、提供建议和进行自然对话。</p>
                <div class="feature-tags">
                    <span class="feature-tag">💡 智能问答</span>
                    <span class="feature-tag">🔄 流式对话</span>
                    <span class="feature-tag">🎯 专业建议</span>
                    <span class="feature-tag">📁 文件上传</span>
                </div>
            </div>
        `;
        this.uploadedFiles.clear();
        this.updateFileDisplay();
    }

    async handleFileUpload(event) {
        """处理文件上传"""
        const files = event.target.files;
        if (!files.length || !this.sessionId) return;

        for (const file of files) {
            await this.uploadFile(file);
        }

        // 清空文件输入
        event.target.value = '';
    }

    async uploadFile(file) {
        """上传单个文件"""
        try {
            // 检查文件大小（10MB限制）
            const maxSize = 10 * 1024 * 1024;
            if (file.size > maxSize) {
                alert(`文件 ${file.name} 大小超过10MB限制`);
                return;
            }

            const formData = new FormData();
            formData.append('session_id', this.sessionId);
            formData.append('file', file);

            const response = await fetch(`${this.apiBase}/api/upload`, {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const data = await response.json();
                this.uploadedFiles.set(data.file_id, {
                    id: data.file_id,
                    name: data.filename,
                    type: data.file_type,
                    size: data.file_size
                });
                this.updateFileDisplay();
                console.log('✅ 文件上传成功:', data.filename);
            } else {
                const error = await response.json();
                alert(`文件上传失败: ${error.detail}`);
            }
        } catch (error) {
            console.error('❌ 文件上传异常:', error);
            alert(`文件上传失败: ${error.message}`);
        }
    }

    updateFileDisplay() {
        """更新文件显示"""
        const hasFiles = this.uploadedFiles.size > 0;
        this.elements.fileUploadArea.style.display = hasFiles ? 'block' : 'none';

        if (hasFiles) {
            const filesHtml = Array.from(this.uploadedFiles.values()).map(file => `
                <div class="file-item" data-file-id="${file.id}">
                    <span class="material-icons file-icon">description</span>
                    <span class="file-name" title="${file.name}">${file.name}</span>
                    <span class="file-size">${this.formatFileSize(file.size)}</span>
                    <button class="remove-file" onclick="chatApp.removeFile('${file.id}')">
                        <span class="material-icons">close</span>
                    </button>
                </div>
            `).join('');
            this.elements.uploadedFiles.innerHTML = filesHtml;
        }
    }

    removeFile(fileId) {
        """移除文件"""
        this.uploadedFiles.delete(fileId);
        this.updateFileDisplay();
    }

    formatFileSize(bytes) {
        """格式化文件大小"""
        if (bytes < 1024) return bytes + ' B';
        if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
        return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    }
    
    async checkSystemStatus() {
        try {
            const response = await fetch(`${this.apiBase}/api/health`);
            const data = await response.json();
            
            this.isConnected = data.status === 'healthy' && data.chat_service === 'ready';
            this.updateStatusIndicator();
            
            if (this.elements.systemStatus) {
                this.elements.systemStatus.textContent = this.isConnected ? '正常运行' : '服务异常';
            }
            
        } catch (error) {
            console.error('系统状态检查失败:', error);
            this.isConnected = false;
            this.updateStatusIndicator();
        }
    }
    
    updateStatusIndicator() {
        const statusDot = document.querySelector('.status-dot');
        const statusText = this.elements.statusText;
        
        if (this.isConnected) {
            statusDot.style.background = '#34a853';
            statusText.textContent = '已连接';
        } else {
            statusDot.style.background = '#ea4335';
            statusText.textContent = '连接失败';
        }
    }
    
    updateCharCount() {
        const count = this.elements.messageInput.value.length;
        this.elements.charCount.textContent = `${count}/2000`;
        
        if (count > 1800) {
            this.elements.charCount.style.color = '#ea4335';
        } else {
            this.elements.charCount.style.color = '#5f6368';
        }
    }
    
    updateSendButton() {
        const hasText = this.elements.messageInput.value.trim().length > 0;
        this.elements.sendBtn.disabled = !hasText || this.isStreaming || !this.isConnected;
    }
    
    autoResize(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
    
    async sendMessage() {
        const message = this.elements.messageInput.value.trim();
        if (!message || this.isStreaming || !this.isConnected) return;
        
        // 添加用户消息到界面
        this.addMessage('user', message);
        
        // 清空输入框
        this.elements.messageInput.value = '';
        this.updateCharCount();
        this.updateSendButton();
        this.autoResize(this.elements.messageInput);
        
        // 开始流式对话
        await this.startStreamingChat(message);
    }
    
    async startStreamingChat(message) {
        this.isStreaming = true;
        this.updateSendButton();
        
        // 添加AI消息占位符
        const aiMessageId = this.addMessage('assistant', '');
        const aiMessageElement = document.querySelector(`[data-message-id="${aiMessageId}"] .message-text`);
        
        try {
            // 创建SSE连接
            const response = await fetch(`${this.apiBase}/api/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    session_id: this.sessionId,
                    file_ids: Array.from(this.uploadedFiles.keys())
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let aiResponse = '';
            
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                
                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');
                
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data.trim()) {
                            try {
                                const parsed = JSON.parse(data);
                                await this.handleStreamData(parsed, aiMessageElement, aiResponse);
                                
                                if (parsed.type === 'chunk') {
                                    aiResponse += parsed.data.content || '';
                                }
                            } catch (e) {
                                console.warn('解析SSE数据失败:', e, data);
                            }
                        }
                    }
                }
            }
            
        } catch (error) {
            console.error('流式聊天失败:', error);
            aiMessageElement.innerHTML = `<span style="color: #ea4335;">❌ 对话失败: ${error.message}</span>`;
        } finally {
            this.isStreaming = false;
            this.updateSendButton();
        }
    }
    
    async handleStreamData(data, messageElement, currentResponse) {
        switch (data.type) {
            case 'session_created':
                this.sessionId = data.data.session_id;
                this.updateSessionDisplay();
                console.log('✅ 会话已创建:', this.sessionId);
                break;

            case 'connected':
                messageElement.innerHTML = '<span style="color: #5f6368;">🔗 已连接，等待回复...</span>';
                break;

            case 'start':
                messageElement.innerHTML = '<span style="color: #5f6368;">🤔 AI正在思考...</span>';
                break;
                
            case 'chunk':
                // 累积显示流式内容
                const fullText = currentResponse + (data.data.content || '');
                messageElement.innerHTML = this.formatMessageText(fullText);
                this.scrollToBottom();
                break;
                
            case 'complete':
                messageElement.innerHTML = this.formatMessageText(data.data.full_response || currentResponse);
                this.scrollToBottom();
                // 清空已上传的文件
                this.uploadedFiles.clear();
                this.updateFileDisplay();
                break;
                
            case 'error':
                messageElement.innerHTML = `<span style="color: #ea4335;">❌ ${data.data.message}</span>`;
                break;
                
            case 'end':
                // 对话结束，无需特殊处理
                break;
        }
    }
    
    addMessage(role, content) {
        const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const timestamp = new Date().toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        const messageHtml = `
            <div class="message ${role}" data-message-id="${messageId}">
                <div class="message-avatar">
                    <span class="material-icons">
                        ${role === 'user' ? 'person' : 'smart_toy'}
                    </span>
                </div>
                <div class="message-content">
                    <div class="message-text">${this.formatMessageText(content)}</div>
                    <div class="message-time">${timestamp}</div>
                </div>
            </div>
        `;
        
        // 移除欢迎消息（如果存在）
        const welcomeMessage = this.elements.chatMessages.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
        
        this.elements.chatMessages.insertAdjacentHTML('beforeend', messageHtml);
        this.scrollToBottom();
        
        return messageId;
    }
    
    formatMessageText(text) {
        if (!text) return '';
        
        // 简单的文本格式化
        return text
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>');
    }
    
    scrollToBottom() {
        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
    }
    
    async clearChat() {
        if (confirm('确定要清空所有对话记录吗？')) {
            try {
                await fetch(`${this.apiBase}/api/clear`, { method: 'POST' });
                
                // 清空界面
                this.elements.chatMessages.innerHTML = `
                    <div class="welcome-message">
                        <div class="welcome-icon">
                            <span class="material-icons">waving_hand</span>
                        </div>
                        <h2>欢迎使用智能聊天系统</h2>
                        <p>我是基于AutoGen + DeepSeek的AI助手，可以帮助您解答问题、提供建议和进行自然对话。</p>
                        <div class="feature-tags">
                            <span class="feature-tag">💡 智能问答</span>
                            <span class="feature-tag">🔄 流式对话</span>
                            <span class="feature-tag">🎯 专业建议</span>
                        </div>
                    </div>
                `;
                
            } catch (error) {
                console.error('清空对话失败:', error);
                alert('清空对话失败，请稍后重试');
            }
        }
    }
    
    showSettings() {
        this.elements.settingsPanel.classList.add('show');
    }
    
    hideSettings() {
        this.elements.settingsPanel.classList.remove('show');
    }
    
    loadSettings() {
        // 从localStorage加载设置
        const settings = JSON.parse(localStorage.getItem('chatSettings') || '{}');
        
        this.elements.themeSelect.value = settings.theme || 'light';
        this.elements.fontSizeSelect.value = settings.fontSize || 'medium';
        this.elements.streamToggle.checked = settings.streaming !== false;
        
        this.applySettings();
    }
    
    applySettings() {
        const settings = {
            theme: this.elements.themeSelect.value,
            fontSize: this.elements.fontSizeSelect.value,
            streaming: this.elements.streamToggle.checked
        };
        
        // 保存设置
        localStorage.setItem('chatSettings', JSON.stringify(settings));
        
        // 应用主题
        document.body.className = `theme-${settings.theme} font-${settings.fontSize}`;
        
        console.log('设置已应用:', settings);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.chatApp = new ChatApp();
    console.log('🚀 智能聊天系统已启动');
});
