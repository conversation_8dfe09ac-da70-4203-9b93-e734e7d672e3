/**
 * 智能聊天系统 - 前端JavaScript
 * 处理聊天逻辑、SSE流式通信和UI交互
 */

class ChatApp {
    constructor() {
        this.apiBase = window.location.origin;
        this.isConnected = false;
        this.isStreaming = false;
        this.currentEventSource = null;
        this.sessionId = this.generateSessionId();
        
        this.initializeElements();
        this.bindEvents();
        this.checkSystemStatus();
        this.loadSettings();
    }
    
    initializeElements() {
        // 获取DOM元素
        this.elements = {
            messageInput: document.getElementById('messageInput'),
            sendBtn: document.getElementById('sendBtn'),
            chatMessages: document.getElementById('chatMessages'),
            clearBtn: document.getElementById('clearBtn'),
            settingsBtn: document.getElementById('settingsBtn'),
            settingsPanel: document.getElementById('settingsPanel'),
            closeSettingsBtn: document.getElementById('closeSettingsBtn'),
            statusIndicator: document.getElementById('statusIndicator'),
            statusText: document.querySelector('.status-text'),
            loadingOverlay: document.getElementById('loadingOverlay'),
            charCount: document.getElementById('charCount'),
            systemStatus: document.getElementById('systemStatus'),
            themeSelect: document.getElementById('themeSelect'),
            fontSizeSelect: document.getElementById('fontSizeSelect'),
            streamToggle: document.getElementById('streamToggle')
        };
    }
    
    bindEvents() {
        // 发送按钮点击
        this.elements.sendBtn.addEventListener('click', () => this.sendMessage());
        
        // 输入框事件
        this.elements.messageInput.addEventListener('input', (e) => {
            this.updateCharCount();
            this.updateSendButton();
            this.autoResize(e.target);
        });
        
        this.elements.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // 清空对话
        this.elements.clearBtn.addEventListener('click', () => this.clearChat());
        
        // 设置面板
        this.elements.settingsBtn.addEventListener('click', () => this.showSettings());
        this.elements.closeSettingsBtn.addEventListener('click', () => this.hideSettings());
        
        // 设置变更
        this.elements.themeSelect.addEventListener('change', () => this.applySettings());
        this.elements.fontSizeSelect.addEventListener('change', () => this.applySettings());
        this.elements.streamToggle.addEventListener('change', () => this.applySettings());
        
        // 点击外部关闭设置面板
        document.addEventListener('click', (e) => {
            if (!this.elements.settingsPanel.contains(e.target) && 
                !this.elements.settingsBtn.contains(e.target)) {
                this.hideSettings();
            }
        });
    }
    
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    async checkSystemStatus() {
        try {
            const response = await fetch(`${this.apiBase}/api/health`);
            const data = await response.json();
            
            this.isConnected = data.status === 'healthy' && data.chat_service === 'ready';
            this.updateStatusIndicator();
            
            if (this.elements.systemStatus) {
                this.elements.systemStatus.textContent = this.isConnected ? '正常运行' : '服务异常';
            }
            
        } catch (error) {
            console.error('系统状态检查失败:', error);
            this.isConnected = false;
            this.updateStatusIndicator();
        }
    }
    
    updateStatusIndicator() {
        const statusDot = document.querySelector('.status-dot');
        const statusText = this.elements.statusText;
        
        if (this.isConnected) {
            statusDot.style.background = '#34a853';
            statusText.textContent = '已连接';
        } else {
            statusDot.style.background = '#ea4335';
            statusText.textContent = '连接失败';
        }
    }
    
    updateCharCount() {
        const count = this.elements.messageInput.value.length;
        this.elements.charCount.textContent = `${count}/2000`;
        
        if (count > 1800) {
            this.elements.charCount.style.color = '#ea4335';
        } else {
            this.elements.charCount.style.color = '#5f6368';
        }
    }
    
    updateSendButton() {
        const hasText = this.elements.messageInput.value.trim().length > 0;
        this.elements.sendBtn.disabled = !hasText || this.isStreaming || !this.isConnected;
    }
    
    autoResize(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
    
    async sendMessage() {
        const message = this.elements.messageInput.value.trim();
        if (!message || this.isStreaming || !this.isConnected) return;
        
        // 添加用户消息到界面
        this.addMessage('user', message);
        
        // 清空输入框
        this.elements.messageInput.value = '';
        this.updateCharCount();
        this.updateSendButton();
        this.autoResize(this.elements.messageInput);
        
        // 开始流式对话
        await this.startStreamingChat(message);
    }
    
    async startStreamingChat(message) {
        this.isStreaming = true;
        this.updateSendButton();
        
        // 添加AI消息占位符
        const aiMessageId = this.addMessage('assistant', '');
        const aiMessageElement = document.querySelector(`[data-message-id="${aiMessageId}"] .message-text`);
        
        try {
            // 创建SSE连接
            const response = await fetch(`${this.apiBase}/api/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    session_id: this.sessionId
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let aiResponse = '';
            
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                
                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');
                
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data.trim()) {
                            try {
                                const parsed = JSON.parse(data);
                                await this.handleStreamData(parsed, aiMessageElement, aiResponse);
                                
                                if (parsed.type === 'chunk') {
                                    aiResponse += parsed.data.content || '';
                                }
                            } catch (e) {
                                console.warn('解析SSE数据失败:', e, data);
                            }
                        }
                    }
                }
            }
            
        } catch (error) {
            console.error('流式聊天失败:', error);
            aiMessageElement.innerHTML = `<span style="color: #ea4335;">❌ 对话失败: ${error.message}</span>`;
        } finally {
            this.isStreaming = false;
            this.updateSendButton();
        }
    }
    
    async handleStreamData(data, messageElement, currentResponse) {
        switch (data.type) {
            case 'connected':
                messageElement.innerHTML = '<span style="color: #5f6368;">🔗 已连接，等待回复...</span>';
                break;
                
            case 'start':
                messageElement.innerHTML = '<span style="color: #5f6368;">🤔 AI正在思考...</span>';
                break;
                
            case 'chunk':
                // 累积显示流式内容
                const fullText = currentResponse + (data.data.content || '');
                messageElement.innerHTML = this.formatMessageText(fullText);
                this.scrollToBottom();
                break;
                
            case 'complete':
                messageElement.innerHTML = this.formatMessageText(data.data.full_response || currentResponse);
                this.scrollToBottom();
                break;
                
            case 'error':
                messageElement.innerHTML = `<span style="color: #ea4335;">❌ ${data.data.message}</span>`;
                break;
                
            case 'end':
                // 对话结束，无需特殊处理
                break;
        }
    }
    
    addMessage(role, content) {
        const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const timestamp = new Date().toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        const messageHtml = `
            <div class="message ${role}" data-message-id="${messageId}">
                <div class="message-avatar">
                    <span class="material-icons">
                        ${role === 'user' ? 'person' : 'smart_toy'}
                    </span>
                </div>
                <div class="message-content">
                    <div class="message-text">${this.formatMessageText(content)}</div>
                    <div class="message-time">${timestamp}</div>
                </div>
            </div>
        `;
        
        // 移除欢迎消息（如果存在）
        const welcomeMessage = this.elements.chatMessages.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
        
        this.elements.chatMessages.insertAdjacentHTML('beforeend', messageHtml);
        this.scrollToBottom();
        
        return messageId;
    }
    
    formatMessageText(text) {
        if (!text) return '';
        
        // 简单的文本格式化
        return text
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>');
    }
    
    scrollToBottom() {
        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
    }
    
    async clearChat() {
        if (confirm('确定要清空所有对话记录吗？')) {
            try {
                await fetch(`${this.apiBase}/api/clear`, { method: 'POST' });
                
                // 清空界面
                this.elements.chatMessages.innerHTML = `
                    <div class="welcome-message">
                        <div class="welcome-icon">
                            <span class="material-icons">waving_hand</span>
                        </div>
                        <h2>欢迎使用智能聊天系统</h2>
                        <p>我是基于AutoGen + DeepSeek的AI助手，可以帮助您解答问题、提供建议和进行自然对话。</p>
                        <div class="feature-tags">
                            <span class="feature-tag">💡 智能问答</span>
                            <span class="feature-tag">🔄 流式对话</span>
                            <span class="feature-tag">🎯 专业建议</span>
                        </div>
                    </div>
                `;
                
            } catch (error) {
                console.error('清空对话失败:', error);
                alert('清空对话失败，请稍后重试');
            }
        }
    }
    
    showSettings() {
        this.elements.settingsPanel.classList.add('show');
    }
    
    hideSettings() {
        this.elements.settingsPanel.classList.remove('show');
    }
    
    loadSettings() {
        // 从localStorage加载设置
        const settings = JSON.parse(localStorage.getItem('chatSettings') || '{}');
        
        this.elements.themeSelect.value = settings.theme || 'light';
        this.elements.fontSizeSelect.value = settings.fontSize || 'medium';
        this.elements.streamToggle.checked = settings.streaming !== false;
        
        this.applySettings();
    }
    
    applySettings() {
        const settings = {
            theme: this.elements.themeSelect.value,
            fontSize: this.elements.fontSizeSelect.value,
            streaming: this.elements.streamToggle.checked
        };
        
        // 保存设置
        localStorage.setItem('chatSettings', JSON.stringify(settings));
        
        // 应用主题
        document.body.className = `theme-${settings.theme} font-${settings.fontSize}`;
        
        console.log('设置已应用:', settings);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.chatApp = new ChatApp();
    console.log('🚀 智能聊天系统已启动');
});
