Metadata-Version: 2.4
Name: pyautogen
Version: 0.7.6
Summary: A programming framework for agentic AI
Project-URL: Homepage, https://ag2.ai/
Project-URL: Documentation, https://docs.ag2.ai/docs/Home
Project-URL: Tracker, https://github.com/ag2ai/ag2/issues
Project-URL: Source, https://github.com/ag2ai/ag2
Project-URL: Discord, https://discord.gg/pAbnFJrkgZ
Author-email: <PERSON> & <PERSON> <<EMAIL>>
License-File: LICENSE
License-File: NOTICE.md
Keywords: ag2,ag2.ai,ag2ai,agent,agentic,ai,autogen,pyautogen
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Information Technology
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Application Frameworks
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: <3.14,>=3.9
Requires-Dist: asyncer==0.0.8
Requires-Dist: diskcache
Requires-Dist: docker
Requires-Dist: fast-depends<3,>=2.4.12
Requires-Dist: httpx<1,>=0.28.1
Requires-Dist: packaging
Requires-Dist: pydantic<3,>=2.6.1
Requires-Dist: python-dotenv
Requires-Dist: termcolor
Requires-Dist: tiktoken
Provides-Extra: anthropic
Requires-Dist: anthropic[vertex]>=0.23.1; extra == 'anthropic'
Provides-Extra: autobuild
Requires-Dist: chromadb; extra == 'autobuild'
Requires-Dist: huggingface-hub; extra == 'autobuild'
Requires-Dist: pysqlite3-binary; extra == 'autobuild'
Requires-Dist: sentence-transformers; extra == 'autobuild'
Provides-Extra: bedrock
Requires-Dist: boto3>=1.34.149; extra == 'bedrock'
Provides-Extra: blendsearch
Requires-Dist: flaml[blendsearch]; extra == 'blendsearch'
Provides-Extra: browser-use
Requires-Dist: browser-use==0.1.37; extra == 'browser-use'
Provides-Extra: captainagent
Requires-Dist: chromadb; extra == 'captainagent'
Requires-Dist: huggingface-hub; extra == 'captainagent'
Requires-Dist: pandas; extra == 'captainagent'
Requires-Dist: pysqlite3-binary; extra == 'captainagent'
Requires-Dist: sentence-transformers; extra == 'captainagent'
Provides-Extra: cerebras
Requires-Dist: cerebras-cloud-sdk>=1.0.0; extra == 'cerebras'
Provides-Extra: cohere
Requires-Dist: cohere>=5.13.5; extra == 'cohere'
Provides-Extra: commsagent-discord
Requires-Dist: discord-py<2.5,>=2.4.0; extra == 'commsagent-discord'
Provides-Extra: commsagent-slack
Requires-Dist: slack-sdk<3.40,>=3.33.0; extra == 'commsagent-slack'
Provides-Extra: commsagent-telegram
Requires-Dist: telethon<2,>=1.38.1; extra == 'commsagent-telegram'
Provides-Extra: cosmosdb
Requires-Dist: azure-cosmos>=4.2.0; extra == 'cosmosdb'
Provides-Extra: crawl4ai
Requires-Dist: crawl4ai<0.5,>=0.4.247; extra == 'crawl4ai'
Provides-Extra: dev
Requires-Dist: cairosvg; extra == 'dev'
Requires-Dist: codespell==2.4.1; extra == 'dev'
Requires-Dist: detect-secrets==1.5.0; extra == 'dev'
Requires-Dist: fastapi==0.115.8; extra == 'dev'
Requires-Dist: ipykernel==6.29.5; extra == 'dev'
Requires-Dist: jinja2==3.1.5; extra == 'dev'
Requires-Dist: mdx-include==1.4.2; extra == 'dev'
Requires-Dist: mike==2.1.3; extra == 'dev'
Requires-Dist: mkdocs-git-revision-date-localized-plugin==1.3.0; extra == 'dev'
Requires-Dist: mkdocs-glightbox==0.4.0; extra == 'dev'
Requires-Dist: mkdocs-literate-nav==0.6.1; extra == 'dev'
Requires-Dist: mkdocs-macros-plugin==1.3.7; extra == 'dev'
Requires-Dist: mkdocs-material==9.6.4; extra == 'dev'
Requires-Dist: mkdocs-minify-plugin==0.8.0; extra == 'dev'
Requires-Dist: mkdocstrings[python]==0.28.1; extra == 'dev'
Requires-Dist: mock==5.1.0; extra == 'dev'
Requires-Dist: mypy==1.15.0; extra == 'dev'
Requires-Dist: nbclient==0.10.2; extra == 'dev'
Requires-Dist: nbconvert==7.16.6; extra == 'dev'
Requires-Dist: nbformat==5.10.4; extra == 'dev'
Requires-Dist: openai>=1.58; extra == 'dev'
Requires-Dist: pandas==2.2.3; extra == 'dev'
Requires-Dist: pdoc3==0.11.5; extra == 'dev'
Requires-Dist: pillow; extra == 'dev'
Requires-Dist: pre-commit==4.1.0; extra == 'dev'
Requires-Dist: pytest-asyncio==0.25.3; extra == 'dev'
Requires-Dist: pytest-cov==6.0.0; extra == 'dev'
Requires-Dist: pytest==8.3.4; extra == 'dev'
Requires-Dist: pyupgrade-directories==0.3.0; extra == 'dev'
Requires-Dist: pyyaml==6.0.2; extra == 'dev'
Requires-Dist: ruff==0.9.7; extra == 'dev'
Requires-Dist: termcolor==2.5.0; extra == 'dev'
Requires-Dist: toml==0.10.2; extra == 'dev'
Requires-Dist: typer==0.15.1; extra == 'dev'
Requires-Dist: uv==0.6.2; extra == 'dev'
Provides-Extra: docs
Requires-Dist: cairosvg; extra == 'docs'
Requires-Dist: jinja2==3.1.5; extra == 'docs'
Requires-Dist: mdx-include==1.4.2; extra == 'docs'
Requires-Dist: mike==2.1.3; extra == 'docs'
Requires-Dist: mkdocs-git-revision-date-localized-plugin==1.3.0; extra == 'docs'
Requires-Dist: mkdocs-glightbox==0.4.0; extra == 'docs'
Requires-Dist: mkdocs-literate-nav==0.6.1; extra == 'docs'
Requires-Dist: mkdocs-macros-plugin==1.3.7; extra == 'docs'
Requires-Dist: mkdocs-material==9.6.4; extra == 'docs'
Requires-Dist: mkdocs-minify-plugin==0.8.0; extra == 'docs'
Requires-Dist: mkdocstrings[python]==0.28.1; extra == 'docs'
Requires-Dist: nbclient==0.10.2; extra == 'docs'
Requires-Dist: pdoc3==0.11.5; extra == 'docs'
Requires-Dist: pillow; extra == 'docs'
Requires-Dist: pyyaml==6.0.2; extra == 'docs'
Requires-Dist: termcolor==2.5.0; extra == 'docs'
Requires-Dist: typer==0.15.1; extra == 'docs'
Provides-Extra: flaml
Requires-Dist: flaml; extra == 'flaml'
Requires-Dist: numpy<2.0.0,>=1.24.0; (python_version < '3.13') and extra == 'flaml'
Requires-Dist: numpy>=2.1; (python_version >= '3.13') and extra == 'flaml'
Provides-Extra: gemini
Requires-Dist: google-api-core; extra == 'gemini'
Requires-Dist: google-auth; extra == 'gemini'
Requires-Dist: google-cloud-aiplatform; extra == 'gemini'
Requires-Dist: google-genai<2.0,>=1.2.0; extra == 'gemini'
Requires-Dist: jsonref<2,>=1; extra == 'gemini'
Requires-Dist: jsonschema; extra == 'gemini'
Requires-Dist: pillow; extra == 'gemini'
Provides-Extra: graph
Requires-Dist: matplotlib; extra == 'graph'
Requires-Dist: networkx; extra == 'graph'
Provides-Extra: graph-rag-falkor-db
Requires-Dist: falkordb>=1.0.10; extra == 'graph-rag-falkor-db'
Requires-Dist: graphrag-sdk==0.6.1; extra == 'graph-rag-falkor-db'
Provides-Extra: groq
Requires-Dist: groq>=0.9.0; extra == 'groq'
Provides-Extra: interop
Requires-Dist: crewai[tools]<1,>=0.76; (python_version >= '3.10' and python_version < '3.13') and extra == 'interop'
Requires-Dist: langchain-community<1,>=0.3.12; extra == 'interop'
Requires-Dist: pydantic-ai==0.0.24; extra == 'interop'
Requires-Dist: weaviate-client<5,>=4; (python_version >= '3.10' and python_version < '3.13') and extra == 'interop'
Provides-Extra: interop-crewai
Requires-Dist: crewai[tools]<1,>=0.76; (python_version >= '3.10' and python_version < '3.13') and extra == 'interop-crewai'
Requires-Dist: weaviate-client<5,>=4; (python_version >= '3.10' and python_version < '3.13') and extra == 'interop-crewai'
Provides-Extra: interop-langchain
Requires-Dist: langchain-community<1,>=0.3.12; extra == 'interop-langchain'
Provides-Extra: interop-pydantic-ai
Requires-Dist: pydantic-ai==0.0.24; extra == 'interop-pydantic-ai'
Provides-Extra: jupyter-executor
Requires-Dist: ipykernel>=6.29.0; extra == 'jupyter-executor'
Requires-Dist: jupyter-client>=8.6.0; extra == 'jupyter-executor'
Requires-Dist: jupyter-kernel-gateway; extra == 'jupyter-executor'
Requires-Dist: requests; extra == 'jupyter-executor'
Requires-Dist: websocket-client; extra == 'jupyter-executor'
Provides-Extra: lint
Requires-Dist: codespell==2.4.1; extra == 'lint'
Requires-Dist: pyupgrade-directories==0.3.0; extra == 'lint'
Requires-Dist: ruff==0.9.7; extra == 'lint'
Provides-Extra: lmm
Requires-Dist: pillow; extra == 'lmm'
Requires-Dist: replicate; extra == 'lmm'
Provides-Extra: long-context
Requires-Dist: llmlingua<0.3; extra == 'long-context'
Provides-Extra: mathchat
Requires-Dist: sympy; extra == 'mathchat'
Requires-Dist: wolframalpha; extra == 'mathchat'
Provides-Extra: mistral
Requires-Dist: mistralai>=1.0.1; extra == 'mistral'
Provides-Extra: neo4j
Requires-Dist: docx2txt==0.8; extra == 'neo4j'
Requires-Dist: llama-index-core==0.12.19; extra == 'neo4j'
Requires-Dist: llama-index-graph-stores-neo4j==0.4.6; extra == 'neo4j'
Requires-Dist: llama-index-readers-web==0.3.5; extra == 'neo4j'
Requires-Dist: llama-index==0.12.19; extra == 'neo4j'
Provides-Extra: ollama
Requires-Dist: fix-busted-json>=0.0.18; extra == 'ollama'
Requires-Dist: ollama>=0.4.5; extra == 'ollama'
Provides-Extra: openai
Requires-Dist: openai>=1.58; extra == 'openai'
Provides-Extra: openai-realtime
Requires-Dist: autogen[openai]; extra == 'openai-realtime'
Requires-Dist: openai[realtime]; extra == 'openai-realtime'
Provides-Extra: rag
Requires-Dist: chromadb<1,>=0.5; extra == 'rag'
Requires-Dist: docling<3,>=2.15.1; extra == 'rag'
Requires-Dist: llama-index-vector-stores-chroma==0.4.1; extra == 'rag'
Requires-Dist: llama-index<1,>=0.12; extra == 'rag'
Requires-Dist: selenium<5,>=4.28.1; extra == 'rag'
Requires-Dist: webdriver-manager==4.0.2; extra == 'rag'
Provides-Extra: redis
Requires-Dist: redis; extra == 'redis'
Provides-Extra: retrievechat
Requires-Dist: beautifulsoup4; extra == 'retrievechat'
Requires-Dist: chromadb==0.6.3; extra == 'retrievechat'
Requires-Dist: ipython; extra == 'retrievechat'
Requires-Dist: markdownify; extra == 'retrievechat'
Requires-Dist: protobuf==5.29.3; extra == 'retrievechat'
Requires-Dist: pypdf; extra == 'retrievechat'
Requires-Dist: sentence-transformers; extra == 'retrievechat'
Provides-Extra: retrievechat-couchbase
Requires-Dist: beautifulsoup4; extra == 'retrievechat-couchbase'
Requires-Dist: chromadb==0.6.3; extra == 'retrievechat-couchbase'
Requires-Dist: couchbase>=4.3.0; extra == 'retrievechat-couchbase'
Requires-Dist: ipython; extra == 'retrievechat-couchbase'
Requires-Dist: markdownify; extra == 'retrievechat-couchbase'
Requires-Dist: numpy; extra == 'retrievechat-couchbase'
Requires-Dist: protobuf==5.29.3; extra == 'retrievechat-couchbase'
Requires-Dist: pypdf; extra == 'retrievechat-couchbase'
Requires-Dist: sentence-transformers; extra == 'retrievechat-couchbase'
Provides-Extra: retrievechat-mongodb
Requires-Dist: beautifulsoup4; extra == 'retrievechat-mongodb'
Requires-Dist: chromadb==0.6.3; extra == 'retrievechat-mongodb'
Requires-Dist: ipython; extra == 'retrievechat-mongodb'
Requires-Dist: markdownify; extra == 'retrievechat-mongodb'
Requires-Dist: numpy; extra == 'retrievechat-mongodb'
Requires-Dist: protobuf==5.29.3; extra == 'retrievechat-mongodb'
Requires-Dist: pymongo>=4.0.0; extra == 'retrievechat-mongodb'
Requires-Dist: pypdf; extra == 'retrievechat-mongodb'
Requires-Dist: sentence-transformers; extra == 'retrievechat-mongodb'
Provides-Extra: retrievechat-pgvector
Requires-Dist: beautifulsoup4; extra == 'retrievechat-pgvector'
Requires-Dist: chromadb==0.6.3; extra == 'retrievechat-pgvector'
Requires-Dist: ipython; extra == 'retrievechat-pgvector'
Requires-Dist: markdownify; extra == 'retrievechat-pgvector'
Requires-Dist: pgvector>=0.2.5; extra == 'retrievechat-pgvector'
Requires-Dist: protobuf==5.29.3; extra == 'retrievechat-pgvector'
Requires-Dist: psycopg>=3.1.18; (platform_system == 'Linux') and extra == 'retrievechat-pgvector'
Requires-Dist: psycopg[binary]>=3.1.18; (platform_system == 'Windows' or platform_system == 'Darwin') and extra == 'retrievechat-pgvector'
Requires-Dist: pypdf; extra == 'retrievechat-pgvector'
Requires-Dist: sentence-transformers; extra == 'retrievechat-pgvector'
Provides-Extra: retrievechat-qdrant
Requires-Dist: beautifulsoup4; extra == 'retrievechat-qdrant'
Requires-Dist: chromadb==0.6.3; extra == 'retrievechat-qdrant'
Requires-Dist: fastembed>=0.3.1; extra == 'retrievechat-qdrant'
Requires-Dist: ipython; extra == 'retrievechat-qdrant'
Requires-Dist: markdownify; extra == 'retrievechat-qdrant'
Requires-Dist: protobuf==5.29.3; extra == 'retrievechat-qdrant'
Requires-Dist: pypdf; extra == 'retrievechat-qdrant'
Requires-Dist: qdrant-client; extra == 'retrievechat-qdrant'
Requires-Dist: sentence-transformers; extra == 'retrievechat-qdrant'
Provides-Extra: teachable
Requires-Dist: chromadb; extra == 'teachable'
Provides-Extra: test
Requires-Dist: fastapi==0.115.8; extra == 'test'
Requires-Dist: ipykernel==6.29.5; extra == 'test'
Requires-Dist: mock==5.1.0; extra == 'test'
Requires-Dist: nbconvert==7.16.6; extra == 'test'
Requires-Dist: nbformat==5.10.4; extra == 'test'
Requires-Dist: pandas==2.2.3; extra == 'test'
Requires-Dist: pytest-asyncio==0.25.3; extra == 'test'
Requires-Dist: pytest-cov==6.0.0; extra == 'test'
Requires-Dist: pytest==8.3.4; extra == 'test'
Provides-Extra: together
Requires-Dist: together>=1.2; extra == 'together'
Provides-Extra: twilio
Requires-Dist: fastapi<1,>=0.115.0; extra == 'twilio'
Requires-Dist: twilio>=9.3.2; extra == 'twilio'
Requires-Dist: uvicorn<1,>=0.30.6; extra == 'twilio'
Provides-Extra: types
Requires-Dist: fastapi==0.115.8; extra == 'types'
Requires-Dist: ipykernel==6.29.5; extra == 'types'
Requires-Dist: mock==5.1.0; extra == 'types'
Requires-Dist: mypy==1.15.0; extra == 'types'
Requires-Dist: nbconvert==7.16.6; extra == 'types'
Requires-Dist: nbformat==5.10.4; extra == 'types'
Requires-Dist: openai>=1.58; extra == 'types'
Requires-Dist: pandas==2.2.3; extra == 'types'
Requires-Dist: pytest-asyncio==0.25.3; extra == 'types'
Requires-Dist: pytest-cov==6.0.0; extra == 'types'
Requires-Dist: pytest==8.3.4; extra == 'types'
Provides-Extra: websockets
Requires-Dist: websockets<15,>=14.0; extra == 'websockets'
Provides-Extra: websurfer
Requires-Dist: beautifulsoup4; extra == 'websurfer'
Requires-Dist: markdownify; extra == 'websurfer'
Requires-Dist: pathvalidate; extra == 'websurfer'
Requires-Dist: pdfminer-six; extra == 'websurfer'
Description-Content-Type: text/markdown

<a name="readme-top"></a>

<p align="center">
  <!-- The image URL points to the GitHub-hosted content, ensuring it displays correctly on the PyPI website.-->
  <img src="https://raw.githubusercontent.com/ag2ai/ag2/27b37494a6f72b1f8050f6bd7be9a7ff232cf749/website/static/img/ag2.svg" width="150" title="hover text">
  <br>
  <br>
  <img src="https://img.shields.io/pypi/dm/pyautogen?label=PyPI%20downloads">
  <a href="https://badge.fury.io/py/autogen"><img src="https://badge.fury.io/py/autogen.svg"></a>
  <a href="https://github.com/ag2ai/ag2/actions/workflows/python-package.yml">
    <img src="https://github.com/ag2ai/ag2/actions/workflows/python-package.yml/badge.svg">
  </a>
  <img src="https://img.shields.io/badge/3.9%20%7C%203.10%20%7C%203.11%20%7C%203.12-blue">
  <a href="https://discord.gg/pAbnFJrkgZ">
    <img src="https://img.shields.io/discord/1153072414184452236?logo=discord&style=flat">
  </a>
  <a href="https://x.com/ag2oss">
    <img src="https://img.shields.io/twitter/url/https/twitter.com/cloudposse.svg?style=social&label=Follow%20%40ag2ai">
  </a>
</p>

<p align="center">
  <a href="https://docs.ag2.ai/">📚 Documentation</a> |
  <a href="https://github.com/ag2ai/build-with-ag2">💡 Examples</a> |
  <a href="https://docs.ag2.ai/docs/contributor-guide/contributing">🤝 Contributing</a> |
  <a href="#related-papers">📝 Cite paper</a> |
  <a href="https://discord.gg/pAbnFJrkgZ">💬 Join Discord</a>
</p>

<p align="center">
AG2 was evolved from AutoGen. Fully open-sourced. We invite collaborators from all organizations to contribute.
</p>

# AG2: Open-Source AgentOS for AI Agents

AG2 (formerly AutoGen) is an open-source programming framework for building AI agents and facilitating cooperation among multiple agents to solve tasks. AG2 aims to streamline the development and research of agentic AI. It offers features such as agents capable of interacting with each other, facilitates the use of various large language models (LLMs) and tool use support, autonomous and human-in-the-loop workflows, and multi-agent conversation patterns.

The project is currently maintained by a [dynamic group of volunteers](MAINTAINERS.md) from several organizations. Contact project administrators Chi Wang and Qingyun Wu via [<EMAIL>](mailto:<EMAIL>) if you are interested in becoming a maintainer.

## Table of contents

- [AG2: Open-Source AgentOS for AI Agents](#ag2-open-source-agentos-for-ai-agents)
  - [Table of contents](#table-of-contents)
  - [Getting started](#getting-started)
    - [Installation](#installation)
    - [Setup your API keys](#setup-your-api-keys)
    - [Run your first agent](#run-your-first-agent)
  - [Example applications](#example-applications)
  - [Introduction of different agent concepts](#introduction-of-different-agent-concepts)
    - [Conversable agent](#conversable-agent)
    - [Human in the loop](#human-in-the-loop)
    - [Orchestrating multiple agents](#orchestrating-multiple-agents)
    - [Tools](#tools)
    - [Advanced agentic design patterns](#advanced-agentic-design-patterns)
  - [Announcements](#announcements)
  - [Contributors Wall](#contributors-wall)
  - [Code style and linting](#code-style-and-linting)
  - [Related papers](#related-papers)
  - [Cite the project](#cite-the-project)
  - [License](#license)

## Getting started

For a step-by-step walk through of AG2 concepts and code, see [Basic Concepts](https://docs.ag2.ai/docs/user-guide/basic-concepts) in our documentation.

### Installation

AG2 requires **Python version >= 3.9, < 3.14**. AG2 is available via `ag2` (or its alias `pyautogen` or `autogen`) on PyPI.

```bash
pip install ag2
```

Minimal dependencies are installed by default. You can install extra options based on the features you need.

### Setup your API keys

To keep your LLM dependencies neat we recommend using the `OAI_CONFIG_LIST` file to store your API keys.

You can use the sample file `OAI_CONFIG_LIST_sample` as a template.

```json
[
  {
    "model": "gpt-4o",
    "api_key": "<your OpenAI API key here>"
  }
]
```

### Run your first agent

Create a script or a Jupyter Notebook and run your first agent.

```python
from autogen import AssistantAgent, UserProxyAgent, config_list_from_json

llm_config = {
    "config_list": config_list_from_json(env_or_file="OAI_CONFIG_LIST")
}

assistant = AssistantAgent("assistant", llm_config=llm_config)
user_proxy = UserProxyAgent("user_proxy", code_execution_config={"work_dir": "coding", "use_docker": False})
user_proxy.initiate_chat(assistant, message="Plot a chart of NVDA and TESLA stock price change YTD.")
# This initiates an automated chat between the two agents to solve the task
```

## Example applications

We maintain a dedicated repository with a wide range of applications to help you get started with various use cases or check out our collection of jupyter notebooks as a starting point.

- [Build with AG2](https://github.com/ag2ai/build-with-ag2)
- [Jupyter Notebooks](notebook)

## Introduction of different agent concepts

We have several agent concepts in AG2 to help you build your AI agents. We introduce the most common ones here.

- **Conversable Agent**: Agents that are able to send messages, receive messages and generate replies using GenAI models, non-GenAI tools, or human inputs.
- **Human in the loop**: Add human input to the conversation
- **Orchestrating multiple agents**: Users can orchestrate multiple agents with built-in conversation patterns such as swarms, group chats, nested chats, sequential chats or customize the orchestration by registering custom reply methods.
- **Tools**: Programs that can be registered, invoked and executed by agents
- **Advanced Concepts**: AG2 supports more concepts such as structured outputs, rag, code execution, etc.

### Conversable agent

The conversable agent is the most used agent and is created for generating conversations among agents.
It serves as a base class for all agents in AG2.

```python
from autogen import ConversableAgent

# Create an AI agent
assistant = ConversableAgent(
    name="assistant",
    system_message="You are an assistant that responds concisely.",
    llm_config=llm_config
)

# Create another AI agent
fact_checker = ConversableAgent(
    name="fact_checker",
    system_message="You are a fact-checking assistant.",
    llm_config=llm_config
)

# Start the conversation
assistant.initiate_chat(
    recipient=fact_checker,
    message="What is AG2?",
    max_turns=2
)
```

### Human in the loop

Sometimes your wished workflow requires human input. Therefore you can enable the human in the loop feature.

If you set `human_input_mode` to `ALWAYS` on ConversableAgent you can give human input to the conversation.

There are three modes for `human_input_mode`: `ALWAYS`, `NEVER`, `TERMINATE`.

We created a class which sets the `human_input_mode` to `ALWAYS` for you. Its called `UserProxyAgent`.

```python
from autogen import ConversableAgent

# Create an AI agent
assistant = ConversableAgent(
    name="assistant",
    system_message="You are a helpful assistant.",
    llm_config=llm_config
)

# Create a human agent with manual input mode
human = ConversableAgent(
    name="human",
    human_input_mode="ALWAYS"
)
# or
human = UserProxyAgent(name="human", code_execution_config={"work_dir": "coding", "use_docker": False})

# Start the chat
human.initiate_chat(
    recipient=assistant,
    message="Hello! What's 2 + 2?"
)

```

### Orchestrating multiple agents

Users can define their own orchestration patterns using the flexible programming interface from AG2.

Additionally AG2 provides multiple built-in patterns to orchestrate multiple agents, such as `GroupChat` and `Swarm`.

Both concepts are used to orchestrate multiple agents to solve a task.

The group chat works like a chat where each registered agent can participate in the conversation.

```python
from autogen import ConversableAgent, GroupChat, GroupChatManager

# Create AI agents
teacher = ConversableAgent(name="teacher", system_message="You suggest lesson topics.")
planner = ConversableAgent(name="planner", system_message="You create lesson plans.")
reviewer = ConversableAgent(name="reviewer", system_message="You review lesson plans.")

# Create GroupChat
groupchat = GroupChat(agents=[teacher, planner, reviewer], speaker_selection_method="auto")

# Create the GroupChatManager, it will manage the conversation and uses an LLM to select the next agent
manager = GroupChatManager(name="manager", groupchat=groupchat)

# Start the conversation
teacher.initiate_chat(manager, "Create a lesson on photosynthesis.")
```

The swarm requires a more rigid structure and the flow needs to be defined with hand-off, post-tool, and post-work transitions from an agent to another agent.

Read more about it in the [documentation](https://docs.ag2.ai/docs/user-guide/advanced-concepts/conversation-patterns-deep-dive)

### Tools

Agents gain significant utility through tools as they provide access to external data, APIs, and functionality.

```python
from datetime import datetime
from typing import Annotated

from autogen import ConversableAgent, register_function

# 1. Our tool, returns the day of the week for a given date
def get_weekday(date_string: Annotated[str, "Format: YYYY-MM-DD"]) -> str:
    date = datetime.strptime(date_string, "%Y-%m-%d")
    return date.strftime("%A")

# 2. Agent for determining whether to run the tool
date_agent = ConversableAgent(
    name="date_agent",
    system_message="You get the day of the week for a given date.",
    llm_config=llm_config,
)

# 3. And an agent for executing the tool
executor_agent = ConversableAgent(
    name="executor_agent",
    human_input_mode="NEVER",
)

# 4. Registers the tool with the agents, the description will be used by the LLM
register_function(
    get_weekday,
    caller=date_agent,
    executor=executor_agent,
    description="Get the day of the week for a given date",
)

# 5. Two-way chat ensures the executor agent follows the suggesting agent
chat_result = executor_agent.initiate_chat(
    recipient=date_agent,
    message="I was born on the 25th of March 1995, what day was it?",
    max_turns=1,
)
```

### Advanced agentic design patterns

AG2 supports more advanced concepts to help you build your AI agent workflows. You can find more information in the documentation.

- [Structured Output](https://docs.ag2.ai/docs/user-guide/basic-concepts/structured-outputs)
- [Ending a conversation](https://docs.ag2.ai/docs/user-guide/basic-concepts/ending-a-chat)
- [Retrieval Augmented Generation (RAG)](https://docs.ag2.ai/docs/user-guide/advanced-concepts/rag)
- [Code Execution](https://docs.ag2.ai/docs/user-guide/advanced-concepts/code-execution)
- [Tools with Secrets](https://docs.ag2.ai/docs/user-guide/advanced-concepts/tools-with-secrets)

## Announcements

🔥 🎉 **Nov 11, 2024:** We are evolving AutoGen into **AG2**!
A new organization [AG2AI](https://github.com/ag2ai) is created to host the development of AG2 and related projects with open governance. Check [AG2's new look](https://ag2.ai/).

📄 **License:**
We adopt the Apache 2.0 license from v0.3. This enhances our commitment to open-source collaboration while providing additional protections for contributors and users alike.

🎉 May 29, 2024: DeepLearning.ai launched a new short course [AI Agentic Design Patterns with AutoGen](https://www.deeplearning.ai/short-courses/ai-agentic-design-patterns-with-autogen), made in collaboration with Microsoft and Penn State University, and taught by AutoGen creators [Chi Wang](https://github.com/sonichi) and [Qingyun Wu](https://github.com/qingyun-wu).

🎉 May 24, 2024: Foundation Capital published an article on [Forbes: The Promise of Multi-Agent AI](https://www.forbes.com/sites/joannechen/2024/05/24/the-promise-of-multi-agent-ai/?sh=2c1e4f454d97) and a video [AI in the Real World Episode 2: Exploring Multi-Agent AI and AutoGen with Chi Wang](https://www.youtube.com/watch?v=RLwyXRVvlNk).

🎉 Apr 17, 2024: Andrew Ng cited AutoGen in [The Batch newsletter](https://www.deeplearning.ai/the-batch/issue-245/) and [What's next for AI agentic workflows](https://youtu.be/sal78ACtGTc?si=JduUzN_1kDnMq0vF) at Sequoia Capital's AI Ascent (Mar 26).

[More Announcements](announcements.md)

## Contributors Wall

<a href="https://github.com/ag2ai/ag2/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=ag2ai/ag2&max=204" />
</a>

## Code style and linting

This project uses pre-commit hooks to maintain code quality. Before contributing:

1. Install pre-commit:

```bash
pip install pre-commit
pre-commit install
```

2. The hooks will run automatically on commit, or you can run them manually:

```bash
pre-commit run --all-files
```

## Related papers

- [AutoGen: Enabling Next-Gen LLM Applications via Multi-Agent Conversation](https://arxiv.org/abs/2308.08155)

- [EcoOptiGen: Hyperparameter Optimization for Large Language Model Generation Inference](https://arxiv.org/abs/2303.04673)

- [MathChat: Converse to Tackle Challenging Math Problems with LLM Agents](https://arxiv.org/abs/2306.01337)

- [AgentOptimizer: Offline Training of Language Model Agents with Functions as Learnable Weights](https://arxiv.org/pdf/2402.11359)

- [StateFlow: Enhancing LLM Task-Solving through State-Driven Workflows](https://arxiv.org/abs/2403.11322)

## Cite the project

```
@software{AG2_2024,
author = {Chi Wang and Qingyun Wu and the AG2 Community},
title = {AG2: Open-Source AgentOS for AI Agents},
year = {2024},
url = {https://github.com/ag2ai/ag2},
note = {Available at https://docs.ag2.ai/},
version = {latest}
}
```

## License

This project is licensed under the [Apache License, Version 2.0 (Apache-2.0)](./LICENSE).

This project is a spin-off of [AutoGen](https://github.com/microsoft/autogen) and contains code under two licenses:

- The original code from https://github.com/microsoft/autogen is licensed under the MIT License. See the [LICENSE_original_MIT](./license_original/LICENSE_original_MIT) file for details.

- Modifications and additions made in this fork are licensed under the Apache License, Version 2.0. See the [LICENSE](./LICENSE) file for the full license text.

We have documented these changes for clarity and to ensure transparency with our user and contributor community. For more details, please see the [NOTICE](./NOTICE.md) file.
