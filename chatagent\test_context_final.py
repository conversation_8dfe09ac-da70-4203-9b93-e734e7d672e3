#!/usr/bin/env python3
"""
最终上下文测试 - 验证AI是否真的收到了完整上下文
"""

import requests
import json
import time

def test_context_understanding():
    """测试上下文理解"""
    print("🔍 最终上下文理解测试...")
    
    # 创建会话
    resp = requests.post('http://localhost:8000/api/session/create', json={})
    session_id = resp.json()['session_id']
    print(f"✅ 会话创建: {session_id[:8]}...")
    
    # 第一轮：建立上下文
    print("\n--- 第一轮：建立上下文 ---")
    print("👤 用户: 深圳儿童医院停车方便吗？")
    
    resp = requests.post('http://localhost:8000/api/chat', json={
        'message': '深圳儿童医院停车方便吗？', 
        'session_id': session_id
    }, stream=True)
    
    ai_response1 = ""
    for line in resp.iter_lines():
        if line and line.decode().startswith('data: '):
            data = line.decode()[6:]
            if data.strip():
                try:
                    parsed = json.loads(data)
                    if parsed['type'] == 'complete':
                        ai_response1 = parsed['data']['full_response']
                        break
                except: pass
    
    print(f"🤖 AI回复: {ai_response1[:150]}...")
    
    # 检查会话历史
    print("\n📊 检查会话历史:")
    resp = requests.get(f'http://localhost:8000/api/session/{session_id}/messages')
    if resp.status_code == 200:
        data = resp.json()
        messages = data.get('messages', [])
        print(f"会话中有 {len(messages)} 条消息:")
        for i, msg in enumerate(messages):
            print(f"  {i+1}. {msg['role']}: {msg['content'][:50]}...")
    
    # 等待一下
    time.sleep(1)
    
    # 第二轮：测试上下文理解
    print("\n--- 第二轮：测试上下文理解 ---")
    print("👤 用户: 吃饭呢？")
    
    resp = requests.post('http://localhost:8000/api/chat', json={
        'message': '吃饭呢？', 
        'session_id': session_id
    }, stream=True)
    
    ai_response2 = ""
    for line in resp.iter_lines():
        if line and line.decode().startswith('data: '):
            data = line.decode()[6:]
            if data.strip():
                try:
                    parsed = json.loads(data)
                    if parsed['type'] == 'complete':
                        ai_response2 = parsed['data']['full_response']
                        break
                except: pass
    
    print(f"🤖 AI回复: {ai_response2}")
    
    # 分析结果
    print("\n📊 分析结果:")
    
    # 检查是否理解了上下文
    context_keywords = ['深圳儿童医院', '医院附近', '周边', '餐厅', '食堂', '就餐', '用餐']
    wrong_keywords = ['我不用吃饭', '我不需要吃饭', 'AI不用吃饭', '你是正在吃饭吗']
    
    has_context = any(keyword in ai_response2 for keyword in context_keywords)
    has_wrong_understanding = any(keyword in ai_response2 for keyword in wrong_keywords)
    
    if has_context:
        print("✅ AI正确理解了上下文！知道是问医院附近的餐饮")
    elif has_wrong_understanding:
        print("❌ AI错误理解了问题！以为是问AI是否吃饭")
    else:
        print("⚠️ AI的理解不明确")
    
    # 最终会话历史
    print("\n📊 最终会话历史:")
    resp = requests.get(f'http://localhost:8000/api/session/{session_id}/messages')
    if resp.status_code == 200:
        data = resp.json()
        messages = data.get('messages', [])
        print(f"会话中有 {len(messages)} 条消息:")
        for i, msg in enumerate(messages):
            print(f"  {i+1}. {msg['role']}: {msg['content'][:80]}...")
    
    return has_context

def test_explicit_context():
    """测试明确的上下文问题"""
    print("\n🔍 测试明确的上下文问题...")
    
    # 创建会话
    resp = requests.post('http://localhost:8000/api/session/create', json={})
    session_id = resp.json()['session_id']
    print(f"✅ 会话创建: {session_id[:8]}...")
    
    # 第一轮
    print("\n👤 用户: 深圳儿童医院停车方便吗？")
    resp = requests.post('http://localhost:8000/api/chat', json={
        'message': '深圳儿童医院停车方便吗？', 
        'session_id': session_id
    }, stream=True)
    
    for line in resp.iter_lines():
        if line and line.decode().startswith('data: '):
            data = line.decode()[6:]
            if data.strip():
                try:
                    parsed = json.loads(data)
                    if parsed['type'] == 'complete':
                        print(f"🤖 AI回复: {parsed['data']['full_response'][:100]}...")
                        break
                except: pass
    
    time.sleep(1)
    
    # 第二轮：更明确的问题
    print("\n👤 用户: 那医院附近吃饭方便吗？")
    resp = requests.post('http://localhost:8000/api/chat', json={
        'message': '那医院附近吃饭方便吗？', 
        'session_id': session_id
    }, stream=True)
    
    ai_response = ""
    for line in resp.iter_lines():
        if line and line.decode().startswith('data: '):
            data = line.decode()[6:]
            if data.strip():
                try:
                    parsed = json.loads(data)
                    if parsed['type'] == 'complete':
                        ai_response = parsed['data']['full_response']
                        break
                except: pass
    
    print(f"🤖 AI回复: {ai_response[:150]}...")
    
    # 检查是否理解了"那医院"指的是深圳儿童医院
    if '深圳儿童医院' in ai_response or '该医院' in ai_response:
        print("✅ AI正确理解了'那医院'指代深圳儿童医院")
        return True
    else:
        print("❌ AI没有理解'那医院'的指代关系")
        return False

def main():
    """主测试函数"""
    print("🚀 开始最终上下文测试")
    print("=" * 60)
    
    # 测试1：原始问题
    result1 = test_context_understanding()
    
    # 测试2：明确问题
    result2 = test_explicit_context()
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"省略问题理解: {'✅ 通过' if result1 else '❌ 失败'}")
    print(f"指代关系理解: {'✅ 通过' if result2 else '❌ 失败'}")
    
    if result1 and result2:
        print("🎉 上下文记忆功能完全正常！")
    elif result2:
        print("⚠️ 基础上下文记忆正常，但对高度省略的问题理解有限")
    else:
        print("❌ 上下文记忆功能仍有问题")

if __name__ == "__main__":
    main()
