"""
会话管理模块
实现用户会话管理、多轮对话上下文和文件上传功能
"""

import json
import uuid
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from pathlib import Path
import base64
import hashlib

class SessionManager:
    """
    会话管理器
    负责管理用户会话、对话历史和上传文件
    """
    
    def __init__(self):
        """初始化会话管理器"""
        self.sessions: Dict[str, Dict] = {}
        self.uploaded_files: Dict[str, Dict] = {}
        self.max_session_age = 24 * 60 * 60  # 24小时
        self.max_context_messages = 20  # 最大上下文消息数
        self.upload_dir = Path("uploads")
        self.upload_dir.mkdir(exist_ok=True)
        
        print("✅ 会话管理器初始化完成")
    
    def create_session(self, user_id: str = None) -> str:
        """
        创建新会话
        
        Args:
            user_id: 用户ID（可选）
            
        Returns:
            str: 会话ID
        """
        session_id = str(uuid.uuid4())
        
        self.sessions[session_id] = {
            "session_id": session_id,
            "user_id": user_id or f"user_{int(time.time())}",
            "created_at": datetime.now().isoformat(),
            "last_activity": datetime.now().isoformat(),
            "messages": [],
            "uploaded_files": [],
            "context_summary": "",
            "preferences": {
                "language": "zh-CN",
                "theme": "light",
                "max_tokens": 2000
            }
        }
        
        print(f"🆕 创建新会话: {session_id}")
        return session_id
    
    def get_session(self, session_id: str) -> Optional[Dict]:
        """
        获取会话信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            Dict: 会话信息，如果不存在返回None
        """
        if session_id not in self.sessions:
            return None
        
        session = self.sessions[session_id]
        
        # 检查会话是否过期
        last_activity = datetime.fromisoformat(session["last_activity"])
        if datetime.now() - last_activity > timedelta(seconds=self.max_session_age):
            self.delete_session(session_id)
            return None
        
        return session
    
    def update_session_activity(self, session_id: str):
        """更新会话活动时间"""
        if session_id in self.sessions:
            self.sessions[session_id]["last_activity"] = datetime.now().isoformat()
    
    def add_message(self, session_id: str, role: str, content: str, 
                   file_refs: List[str] = None) -> bool:
        """
        添加消息到会话
        
        Args:
            session_id: 会话ID
            role: 消息角色 (user/assistant)
            content: 消息内容
            file_refs: 文件引用列表
            
        Returns:
            bool: 是否添加成功
        """
        session = self.get_session(session_id)
        if not session:
            return False
        
        message = {
            "id": str(uuid.uuid4()),
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "file_refs": file_refs or []
        }
        
        session["messages"].append(message)
        self.update_session_activity(session_id)
        
        # 限制消息历史长度
        if len(session["messages"]) > self.max_context_messages:
            # 保留最近的消息，但保留第一条系统消息（如果有）
            messages = session["messages"]
            system_messages = [msg for msg in messages if msg["role"] == "system"]
            recent_messages = messages[-(self.max_context_messages-len(system_messages)):]
            session["messages"] = system_messages + recent_messages
        
        print(f"📝 添加消息到会话 {session_id}: {role}")
        return True
    
    def get_conversation_context(self, session_id: str, 
                               include_files: bool = True) -> List[Dict]:
        """
        获取对话上下文
        
        Args:
            session_id: 会话ID
            include_files: 是否包含文件信息
            
        Returns:
            List[Dict]: 对话消息列表
        """
        session = self.get_session(session_id)
        if not session:
            return []
        
        messages = session["messages"].copy()
        
        if include_files:
            # 为包含文件的消息添加文件信息
            for message in messages:
                if message.get("file_refs"):
                    file_info = []
                    for file_id in message["file_refs"]:
                        if file_id in self.uploaded_files:
                            file_data = self.uploaded_files[file_id]
                            file_info.append({
                                "id": file_id,
                                "name": file_data["original_name"],
                                "type": file_data["file_type"],
                                "size": file_data["file_size"]
                            })
                    message["files"] = file_info
        
        return messages
    
    def upload_file(self, session_id: str, file_data: bytes, 
                   filename: str, content_type: str = None) -> Optional[str]:
        """
        上传文件
        
        Args:
            session_id: 会话ID
            file_data: 文件数据
            filename: 文件名
            content_type: 文件类型
            
        Returns:
            str: 文件ID，失败返回None
        """
        session = self.get_session(session_id)
        if not session:
            return None
        
        # 生成文件ID
        file_id = str(uuid.uuid4())
        
        # 计算文件哈希
        file_hash = hashlib.md5(file_data).hexdigest()
        
        # 检测文件类型
        file_type = self._detect_file_type(file_data, filename, content_type)
        
        # 保存文件信息
        file_info = {
            "id": file_id,
            "session_id": session_id,
            "original_name": filename,
            "file_type": file_type,
            "file_size": len(file_data),
            "file_hash": file_hash,
            "upload_time": datetime.now().isoformat(),
            "base64_content": base64.b64encode(file_data).decode('utf-8')
        }
        
        # 存储文件信息
        self.uploaded_files[file_id] = file_info
        session["uploaded_files"].append(file_id)
        
        print(f"📁 文件上传成功: {filename} -> {file_id}")
        return file_id
    
    def get_file(self, file_id: str) -> Optional[Dict]:
        """
        获取文件信息
        
        Args:
            file_id: 文件ID
            
        Returns:
            Dict: 文件信息
        """
        return self.uploaded_files.get(file_id)
    
    def delete_session(self, session_id: str) -> bool:
        """
        删除会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 是否删除成功
        """
        if session_id not in self.sessions:
            return False
        
        session = self.sessions[session_id]
        
        # 删除会话相关的文件
        for file_id in session.get("uploaded_files", []):
            if file_id in self.uploaded_files:
                del self.uploaded_files[file_id]
        
        # 删除会话
        del self.sessions[session_id]
        
        print(f"🗑️ 删除会话: {session_id}")
        return True
    
    def cleanup_expired_sessions(self):
        """清理过期会话"""
        current_time = datetime.now()
        expired_sessions = []
        
        for session_id, session in self.sessions.items():
            last_activity = datetime.fromisoformat(session["last_activity"])
            if current_time - last_activity > timedelta(seconds=self.max_session_age):
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.delete_session(session_id)
        
        if expired_sessions:
            print(f"🧹 清理过期会话: {len(expired_sessions)} 个")
    
    def get_session_stats(self) -> Dict:
        """获取会话统计信息"""
        return {
            "total_sessions": len(self.sessions),
            "total_files": len(self.uploaded_files),
            "active_sessions": len([
                s for s in self.sessions.values()
                if datetime.now() - datetime.fromisoformat(s["last_activity"]) 
                < timedelta(hours=1)
            ])
        }
    
    def _detect_file_type(self, file_data: bytes, filename: str, 
                         content_type: str = None) -> str:
        """
        检测文件类型
        
        Args:
            file_data: 文件数据
            filename: 文件名
            content_type: MIME类型
            
        Returns:
            str: 文件类型
        """
        # 基于文件扩展名判断
        ext = Path(filename).suffix.lower()
        
        if ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
            return 'image'
        elif ext in ['.txt', '.md', '.csv']:
            return 'text'
        elif ext in ['.pdf']:
            return 'pdf'
        elif ext in ['.doc', '.docx']:
            return 'document'
        elif ext in ['.xls', '.xlsx']:
            return 'spreadsheet'
        elif ext in ['.json']:
            return 'json'
        else:
            return 'unknown'


# 全局会话管理器实例
session_manager = SessionManager()
