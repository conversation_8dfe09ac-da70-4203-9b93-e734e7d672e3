"""
智能聊天系统 - FastAPI后端服务器主程序
基于AutoGen 0.7.6 + DeepSeek API + SSE流式输出

主要功能：
1. 提供RESTful API接口服务
2. 支持SSE流式聊天响应
3. 文件上传和管理功能
4. 用户会话管理
5. 静态文件服务（前端页面）

技术栈：
- FastAPI: 现代化Python Web框架
- AutoGen: 微软多智能体对话框架
- DeepSeek: 高性能大语言模型
- SSE: Server-Sent Events流式通信
"""

import json  # JSON数据处理
import asyncio  # 异步编程支持
from datetime import datetime  # 日期时间处理
from typing import Optional  # 类型注解

# FastAPI核心组件导入
from fastapi import FastAPI, Request, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware  # 跨域中间件
from fastapi.responses import StreamingResponse, JSONResponse  # 响应类型
from fastapi.staticfiles import StaticFiles  # 静态文件服务
from pydantic import BaseModel  # 数据模型验证
import uvicorn  # ASGI服务器

# 项目模块导入
from config import SYSTEM_CONFIG, CORS_CONFIG, validate_api_key  # 配置文件
from chat_service import ChatService  # 聊天服务
from session_manager import session_manager  # 会话管理器


# ==================== 数据模型定义 ====================

class ChatRequest(BaseModel):
    """
    聊天请求数据模型
    定义客户端发送聊天消息时的数据结构
    """
    message: str  # 用户输入的消息内容
    session_id: Optional[str] = None  # 会话ID，可选，如果不提供会自动创建
    file_ids: Optional[list] = None  # 关联的文件ID列表，支持文件上传功能

class SessionRequest(BaseModel):
    """
    会话创建请求数据模型
    定义创建新会话时的数据结构
    """
    user_id: Optional[str] = None  # 用户ID，可选，用于标识不同用户


# 创建FastAPI应用
app = FastAPI(
    title=SYSTEM_CONFIG["app_name"],
    version=SYSTEM_CONFIG["version"],
    description=SYSTEM_CONFIG["description"]
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    **CORS_CONFIG
)

# 挂载静态文件（前端）
import os
from pathlib import Path

# 获取正确的前端目录路径
backend_dir = Path(__file__).parent
frontend_dir = backend_dir.parent / "frontend"

if frontend_dir.exists():
    app.mount("/static", StaticFiles(directory=str(frontend_dir)), name="static")
    print(f"✅ 静态文件目录: {frontend_dir}")
else:
    print(f"⚠️ 前端目录不存在: {frontend_dir}")

# 全局聊天服务实例
chat_service: Optional[ChatService] = None


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global chat_service
    
    print("🚀 启动智能聊天系统...")
    print(f"📋 系统信息: {SYSTEM_CONFIG['app_name']} v{SYSTEM_CONFIG['version']}")
    
    # 验证API密钥
    if not validate_api_key():
        print("❌ DeepSeek API密钥未配置，请检查config.py")
        return
    
    try:
        # 初始化聊天服务
        chat_service = ChatService()
        print("✅ 聊天服务初始化成功")
        
    except Exception as e:
        print(f"❌ 聊天服务初始化失败: {e}")
        chat_service = None


@app.get("/")
async def root():
    """根路径 - 系统信息"""
    return {
        "app": SYSTEM_CONFIG["app_name"],
        "version": SYSTEM_CONFIG["version"],
        "description": SYSTEM_CONFIG["description"],
        "status": "running",
        "chat_service_ready": chat_service is not None,
        "timestamp": datetime.now().isoformat()
    }


@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "chat_service": "ready" if chat_service else "not_ready",
        "api_key_configured": validate_api_key(),
        "timestamp": datetime.now().isoformat()
    }


@app.post("/api/chat")
async def chat_endpoint(request: ChatRequest):
    """
    聊天接口 - SSE流式响应
    """
    if not chat_service:
        raise HTTPException(
            status_code=503, 
            detail="聊天服务未就绪，请检查配置"
        )
    
    if not request.message.strip():
        raise HTTPException(
            status_code=400,
            detail="消息内容不能为空"
        )
    
    print(f"💬 收到聊天请求: {request.message[:50]}...")
    
    async def generate_response():
        """生成SSE响应流"""
        try:
            # 发送SSE头部
            yield "data: " + json.dumps({
                "type": "connected",
                "data": {"message": "连接成功，开始对话..."}
            }, ensure_ascii=False) + "\n\n"
            
            # 流式处理聊天（支持文件上传）
            async for chunk in chat_service.chat_stream(
                request.message,
                request.session_id,
                request.file_ids
            ):
                yield f"data: {chunk}\n\n"
                
        except Exception as e:
            # 发送错误信息
            error_data = json.dumps({
                "type": "error",
                "data": {"message": f"处理失败: {str(e)}"}
            }, ensure_ascii=False)
            yield f"data: {error_data}\n\n"
        
        finally:
            # 发送结束信号
            yield "data: " + json.dumps({
                "type": "end",
                "data": {"message": "对话结束"}
            }, ensure_ascii=False) + "\n\n"
    
    return StreamingResponse(
        generate_response(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )


@app.get("/api/history")
async def get_chat_history():
    """获取对话历史"""
    if not chat_service:
        raise HTTPException(status_code=503, detail="聊天服务未就绪")
    
    return {
        "history": chat_service.get_conversation_history(),
        "count": len(chat_service.get_conversation_history()),
        "timestamp": datetime.now().isoformat()
    }


@app.post("/api/clear")
async def clear_chat_history():
    """清空对话历史"""
    if not chat_service:
        raise HTTPException(status_code=503, detail="聊天服务未就绪")
    
    chat_service.clear_history()
    return {
        "message": "对话历史已清空",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/api/status")
async def get_system_status():
    """获取系统状态"""
    session_stats = session_manager.get_session_stats()
    return {
        "system": SYSTEM_CONFIG,
        "services": {
            "chat_service": "ready" if chat_service else "not_ready",
            "api_configured": validate_api_key(),
            "session_manager": "ready"
        },
        "conversation": {
            "history_count": len(chat_service.get_conversation_history()) if chat_service else 0
        },
        "sessions": session_stats,
        "timestamp": datetime.now().isoformat()
    }


# ==================== 会话管理API ====================

@app.post("/api/session/create")
async def create_session(request: SessionRequest):
    """创建新会话"""
    try:
        session_id = session_manager.create_session(request.user_id)
        return {
            "session_id": session_id,
            "message": "会话创建成功",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建会话失败: {str(e)}")


@app.get("/api/session/{session_id}")
async def get_session_info(session_id: str):
    """获取会话信息"""
    session = session_manager.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="会话不存在或已过期")

    return {
        "session": session,
        "timestamp": datetime.now().isoformat()
    }


@app.get("/api/session/{session_id}/messages")
async def get_session_messages(session_id: str):
    """获取会话消息历史"""
    session = session_manager.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="会话不存在或已过期")

    messages = session_manager.get_conversation_context(session_id, include_files=True)
    return {
        "session_id": session_id,
        "messages": messages,
        "count": len(messages),
        "timestamp": datetime.now().isoformat()
    }


@app.delete("/api/session/{session_id}")
async def delete_session(session_id: str):
    """删除会话"""
    success = session_manager.delete_session(session_id)
    if not success:
        raise HTTPException(status_code=404, detail="会话不存在")

    return {
        "message": "会话删除成功",
        "timestamp": datetime.now().isoformat()
    }


# ==================== 文件上传API ====================

@app.post("/api/upload")
async def upload_file(
    session_id: str = Form(...),
    file: UploadFile = File(...)
):
    """上传文件"""
    try:
        # 验证会话
        session = session_manager.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在或已过期")

        # 检查文件大小（限制10MB）
        max_size = 10 * 1024 * 1024  # 10MB
        file_content = await file.read()
        if len(file_content) > max_size:
            raise HTTPException(status_code=413, detail="文件大小超过限制（10MB）")

        # 上传文件
        file_id = session_manager.upload_file(
            session_id=session_id,
            file_data=file_content,
            filename=file.filename,
            content_type=file.content_type
        )

        if not file_id:
            raise HTTPException(status_code=500, detail="文件上传失败")

        file_info = session_manager.get_file(file_id)

        return {
            "file_id": file_id,
            "filename": file.filename,
            "file_type": file_info["file_type"],
            "file_size": file_info["file_size"],
            "message": "文件上传成功",
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")


@app.get("/api/file/{file_id}")
async def get_file_info(file_id: str):
    """获取文件信息"""
    file_info = session_manager.get_file(file_id)
    if not file_info:
        raise HTTPException(status_code=404, detail="文件不存在")

    # 不返回文件内容，只返回元信息
    return {
        "file_id": file_id,
        "filename": file_info["original_name"],
        "file_type": file_info["file_type"],
        "file_size": file_info["file_size"],
        "upload_time": file_info["upload_time"],
        "timestamp": datetime.now().isoformat()
    }


@app.get("/api/session/{session_id}/files")
async def get_session_files(session_id: str):
    """获取会话的所有文件"""
    session = session_manager.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="会话不存在或已过期")

    files = []
    for file_id in session.get("uploaded_files", []):
        file_info = session_manager.get_file(file_id)
        if file_info:
            files.append({
                "file_id": file_id,
                "filename": file_info["original_name"],
                "file_type": file_info["file_type"],
                "file_size": file_info["file_size"],
                "upload_time": file_info["upload_time"]
            })

    return {
        "session_id": session_id,
        "files": files,
        "count": len(files),
        "timestamp": datetime.now().isoformat()
    }


# 异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    print(f"❌ 全局异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "服务器内部错误",
            "detail": str(exc),
            "timestamp": datetime.now().isoformat()
        }
    )


if __name__ == "__main__":
    print("🚀 启动智能聊天系统服务器...")
    uvicorn.run(
        "main:app",
        host=SYSTEM_CONFIG["host"],
        port=SYSTEM_CONFIG["port"],
        reload=SYSTEM_CONFIG["debug"],
        log_level="info"
    )
