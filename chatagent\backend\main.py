"""
智能聊天系统 - FastAPI后端服务器
基于AutoGen 0.5.7 + DeepSeek + SSE流式输出
"""

import json
import asyncio
from datetime import datetime
from typing import Optional

from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import uvicorn

from config import SYSTEM_CONFIG, CORS_CONFIG, validate_api_key
from chat_service import ChatService


# 请求模型
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None


# 创建FastAPI应用
app = FastAPI(
    title=SYSTEM_CONFIG["app_name"],
    version=SYSTEM_CONFIG["version"],
    description=SYSTEM_CONFIG["description"]
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    **CORS_CONFIG
)

# 挂载静态文件（前端）
import os
from pathlib import Path

# 获取正确的前端目录路径
backend_dir = Path(__file__).parent
frontend_dir = backend_dir.parent / "frontend"

if frontend_dir.exists():
    app.mount("/static", StaticFiles(directory=str(frontend_dir)), name="static")
    print(f"✅ 静态文件目录: {frontend_dir}")
else:
    print(f"⚠️ 前端目录不存在: {frontend_dir}")

# 全局聊天服务实例
chat_service: Optional[ChatService] = None


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global chat_service
    
    print("🚀 启动智能聊天系统...")
    print(f"📋 系统信息: {SYSTEM_CONFIG['app_name']} v{SYSTEM_CONFIG['version']}")
    
    # 验证API密钥
    if not validate_api_key():
        print("❌ DeepSeek API密钥未配置，请检查config.py")
        return
    
    try:
        # 初始化聊天服务
        chat_service = ChatService()
        print("✅ 聊天服务初始化成功")
        
    except Exception as e:
        print(f"❌ 聊天服务初始化失败: {e}")
        chat_service = None


@app.get("/")
async def root():
    """根路径 - 系统信息"""
    return {
        "app": SYSTEM_CONFIG["app_name"],
        "version": SYSTEM_CONFIG["version"],
        "description": SYSTEM_CONFIG["description"],
        "status": "running",
        "chat_service_ready": chat_service is not None,
        "timestamp": datetime.now().isoformat()
    }


@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "chat_service": "ready" if chat_service else "not_ready",
        "api_key_configured": validate_api_key(),
        "timestamp": datetime.now().isoformat()
    }


@app.post("/api/chat")
async def chat_endpoint(request: ChatRequest):
    """
    聊天接口 - SSE流式响应
    """
    if not chat_service:
        raise HTTPException(
            status_code=503, 
            detail="聊天服务未就绪，请检查配置"
        )
    
    if not request.message.strip():
        raise HTTPException(
            status_code=400,
            detail="消息内容不能为空"
        )
    
    print(f"💬 收到聊天请求: {request.message[:50]}...")
    
    async def generate_response():
        """生成SSE响应流"""
        try:
            # 发送SSE头部
            yield "data: " + json.dumps({
                "type": "connected",
                "data": {"message": "连接成功，开始对话..."}
            }, ensure_ascii=False) + "\n\n"
            
            # 流式处理聊天
            async for chunk in chat_service.chat_stream(
                request.message, 
                request.session_id
            ):
                yield f"data: {chunk}\n\n"
                
        except Exception as e:
            # 发送错误信息
            error_data = json.dumps({
                "type": "error",
                "data": {"message": f"处理失败: {str(e)}"}
            }, ensure_ascii=False)
            yield f"data: {error_data}\n\n"
        
        finally:
            # 发送结束信号
            yield "data: " + json.dumps({
                "type": "end",
                "data": {"message": "对话结束"}
            }, ensure_ascii=False) + "\n\n"
    
    return StreamingResponse(
        generate_response(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )


@app.get("/api/history")
async def get_chat_history():
    """获取对话历史"""
    if not chat_service:
        raise HTTPException(status_code=503, detail="聊天服务未就绪")
    
    return {
        "history": chat_service.get_conversation_history(),
        "count": len(chat_service.get_conversation_history()),
        "timestamp": datetime.now().isoformat()
    }


@app.post("/api/clear")
async def clear_chat_history():
    """清空对话历史"""
    if not chat_service:
        raise HTTPException(status_code=503, detail="聊天服务未就绪")
    
    chat_service.clear_history()
    return {
        "message": "对话历史已清空",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/api/status")
async def get_system_status():
    """获取系统状态"""
    return {
        "system": SYSTEM_CONFIG,
        "services": {
            "chat_service": "ready" if chat_service else "not_ready",
            "api_configured": validate_api_key()
        },
        "conversation": {
            "history_count": len(chat_service.get_conversation_history()) if chat_service else 0
        },
        "timestamp": datetime.now().isoformat()
    }


# 异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    print(f"❌ 全局异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "服务器内部错误",
            "detail": str(exc),
            "timestamp": datetime.now().isoformat()
        }
    )


if __name__ == "__main__":
    print("🚀 启动智能聊天系统服务器...")
    uvicorn.run(
        "main:app",
        host=SYSTEM_CONFIG["host"],
        port=SYSTEM_CONFIG["port"],
        reload=SYSTEM_CONFIG["debug"],
        log_level="info"
    )
