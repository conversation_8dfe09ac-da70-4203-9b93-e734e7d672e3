## NOTICE

 Copyright (c) 2023 - 2025, AG2ai, Inc., AG2ai open-source projects maintainers and core contributors

This project is a fork of https://github.com/microsoft/autogen.

The [original project](https://github.com/microsoft/autogen) is licensed under the MIT License as detailed in [LICENSE_original_MIT](./license_original/LICENSE_original_MIT). The fork was created from version v0.2.35 of the original project.


This project, i.e., https://github.com/ag2ai/ag2, is licensed under the Apache License, Version 2.0 as detailed in [LICENSE](./LICENSE)


Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.

MIT-licensed contributions:
The MIT license applies to portions of code originating from the [original repository](https://github.com/microsoft/autogen).
For specific details on merged commits, please refer to the project's commit history.

Last updated: 01/16/2025
