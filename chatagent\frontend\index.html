<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能聊天系统 - AutoGen + DeepSeek</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <!-- 主容器 -->
    <div class="app-container">
        <!-- 头部 -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo-icon">
                        <span class="material-icons">smart_toy</span>
                    </div>
                    <h1 class="app-title">智能聊天系统</h1>
                    <span class="app-subtitle">AutoGen + DeepSeek</span>
                </div>
                
                <div class="header-actions">
                    <div class="session-info" id="sessionInfo">
                        <span class="session-id" id="sessionId">会话: 未连接</span>
                    </div>
                    <button class="action-btn" id="newSessionBtn" title="新建会话">
                        <span class="material-icons">add</span>
                    </button>
                    <button class="action-btn" id="clearBtn" title="清空对话">
                        <span class="material-icons">delete_outline</span>
                    </button>
                    <button class="action-btn" id="settingsBtn" title="设置">
                        <span class="material-icons">settings</span>
                    </button>
                    <div class="status-indicator" id="statusIndicator">
                        <span class="status-dot"></span>
                        <span class="status-text">连接中...</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 聊天区域 -->
        <main class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <!-- 欢迎消息 -->
                <div class="welcome-message">
                    <div class="welcome-icon">
                        <span class="material-icons">waving_hand</span>
                    </div>
                    <h2>欢迎使用智能聊天系统</h2>
                    <p>我是基于AutoGen + DeepSeek的AI助手，可以帮助您解答问题、提供建议和进行自然对话。</p>
                    <div class="feature-tags">
                        <span class="feature-tag">💡 智能问答</span>
                        <span class="feature-tag">🔄 流式对话</span>
                        <span class="feature-tag">🎯 专业建议</span>
                    </div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-container">
                <!-- 文件上传区域 -->
                <div class="file-upload-area" id="fileUploadArea" style="display: none;">
                    <div class="uploaded-files" id="uploadedFiles"></div>
                </div>

                <div class="input-wrapper">
                    <button class="file-btn" id="fileBtn" title="上传文件">
                        <span class="material-icons">attach_file</span>
                    </button>
                    <textarea
                        id="messageInput"
                        placeholder="输入您的消息..."
                        rows="1"
                        maxlength="2000"
                    ></textarea>
                    <button class="send-btn" id="sendBtn" disabled>
                        <span class="material-icons">send</span>
                    </button>
                </div>
                <div class="input-footer">
                    <span class="char-count" id="charCount">0/2000</span>
                    <span class="input-hint">按 Enter 发送，Shift+Enter 换行</span>
                </div>

                <!-- 隐藏的文件输入 -->
                <input type="file" id="fileInput" style="display: none;" multiple
                       accept=".txt,.md,.json,.jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx">
            </div>
        </main>

        <!-- 加载指示器 -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>AI正在思考中...</p>
            </div>
        </div>

        <!-- 设置面板 -->
        <div class="settings-panel" id="settingsPanel">
            <div class="settings-content">
                <div class="settings-header">
                    <h3>系统设置</h3>
                    <button class="close-btn" id="closeSettingsBtn">
                        <span class="material-icons">close</span>
                    </button>
                </div>
                <div class="settings-body">
                    <div class="setting-item">
                        <label>主题模式</label>
                        <select id="themeSelect">
                            <option value="light">浅色模式</option>
                            <option value="dark">深色模式</option>
                            <option value="auto">跟随系统</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>字体大小</label>
                        <select id="fontSizeSelect">
                            <option value="small">小</option>
                            <option value="medium" selected>中</option>
                            <option value="large">大</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>流式输出</label>
                        <label class="switch">
                            <input type="checkbox" id="streamToggle" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
                <div class="settings-footer">
                    <div class="system-info">
                        <p><strong>版本:</strong> 1.0.0</p>
                        <p><strong>技术栈:</strong> AutoGen 0.7.6 + DeepSeek</p>
                        <p><strong>状态:</strong> <span id="systemStatus">检查中...</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/chat.js"></script>
</body>
</html>
