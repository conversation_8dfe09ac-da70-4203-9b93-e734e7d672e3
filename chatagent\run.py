#!/usr/bin/env python3
"""
智能聊天系统简化启动脚本
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def main():
    """主函数"""
    print("🤖 智能聊天系统")
    print("基于AutoGen + DeepSeek + FastAPI")
    print("=" * 40)
    
    # 切换到backend目录
    backend_dir = Path(__file__).parent / "backend"
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return
    
    os.chdir(backend_dir)
    print(f"📁 工作目录: {backend_dir}")
    
    # 启动服务器
    print("🚀 启动FastAPI服务器...")
    
    try:
        # 使用uvicorn启动
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        # 启动服务器
        process = subprocess.Popen(cmd)
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        time.sleep(3)
        
        print("✅ 服务器启动成功!")
        print("🌐 访问地址:")
        print("   - 聊天界面: http://localhost:8000/static/index.html")
        print("   - API文档: http://localhost:8000/docs")
        print("   - 系统状态: http://localhost:8000/api/health")
        
        # 自动打开浏览器
        try:
            webbrowser.open("http://localhost:8000/static/index.html")
            print("🌐 已自动打开浏览器")
        except:
            print("⚠️ 无法自动打开浏览器，请手动访问上述地址")
        
        print("\n按 Ctrl+C 停止服务器")
        
        # 等待用户中断
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务器...")
            process.terminate()
            process.wait()
            print("✅ 服务器已停止")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请确保已安装依赖: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
