#!/usr/bin/env python3
"""
智能聊天系统启动脚本
自动检测端口占用并启动服务器
"""

import os
import sys
import subprocess
import socket
import time
import webbrowser
from pathlib import Path

def check_port(port):
    """检查端口是否被占用"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('localhost', port))
            return False  # 端口未被占用
        except OSError:
            return True   # 端口被占用

def kill_process_on_port(port):
    """杀死占用指定端口的进程"""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(
                f'netstat -ano | findstr :{port}',
                shell=True, capture_output=True, text=True
            )
            if result.stdout:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if f':{port}' in line and 'LISTENING' in line:
                        parts = line.split()
                        if len(parts) >= 5:
                            pid = parts[-1]
                            subprocess.run(f'taskkill /F /PID {pid}', shell=True)
                            print(f"✅ 已终止占用端口{port}的进程 (PID: {pid})")
                            return True
        else:  # Linux/Mac
            result = subprocess.run(
                f'lsof -ti:{port}',
                shell=True, capture_output=True, text=True
            )
            if result.stdout:
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    if pid:
                        subprocess.run(f'kill -9 {pid}', shell=True)
                        print(f"✅ 已终止占用端口{port}的进程 (PID: {pid})")
                return True
    except Exception as e:
        print(f"❌ 终止进程失败: {e}")
    return False

def install_dependencies():
    """安装依赖包"""
    print("📦 检查并安装依赖包...")
    
    backend_dir = Path(__file__).parent / "backend"
    requirements_file = backend_dir / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], check=True)
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def start_server():
    """启动服务器"""
    print("🚀 启动智能聊天系统...")
    
    # 切换到backend目录
    backend_dir = Path(__file__).parent / "backend"
    os.chdir(backend_dir)
    
    # 检查端口占用
    port = 8000
    if check_port(port):
        print(f"⚠️ 端口{port}被占用，尝试释放...")
        if kill_process_on_port(port):
            time.sleep(2)  # 等待进程完全终止
        else:
            print(f"❌ 无法释放端口{port}，请手动处理")
            return False
    
    try:
        # 启动FastAPI服务器
        print(f"🌐 启动服务器在端口{port}...")
        
        # 使用uvicorn启动
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "0.0.0.0",
            "--port", str(port),
            "--reload"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        # 启动服务器进程
        process = subprocess.Popen(cmd)
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        time.sleep(3)
        
        # 检查服务器是否启动成功
        if not check_port(port):
            print("❌ 服务器启动失败")
            return False
        
        print("✅ 服务器启动成功!")
        print(f"🌐 访问地址: http://localhost:{port}")
        print(f"📱 前端界面: http://localhost:{port}/static/index.html")
        print(f"📚 API文档: http://localhost:{port}/docs")
        
        # 自动打开浏览器
        try:
            webbrowser.open(f"http://localhost:{port}/static/index.html")
            print("🌐 已自动打开浏览器")
        except:
            print("⚠️ 无法自动打开浏览器，请手动访问上述地址")
        
        print("\n" + "="*50)
        print("🎉 智能聊天系统已启动!")
        print("💡 基于AutoGen 0.5.7 + DeepSeek")
        print("🔄 支持流式对话输出")
        print("🎨 Gemini风格界面设计")
        print("="*50)
        print("\n按 Ctrl+C 停止服务器")
        
        # 等待用户中断
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务器...")
            process.terminate()
            process.wait()
            print("✅ 服务器已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        return False

def main():
    """主函数"""
    print("🤖 智能聊天系统启动器")
    print("=" * 40)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查项目结构
    backend_dir = Path(__file__).parent / "backend"
    frontend_dir = Path(__file__).parent / "frontend"
    
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return
    
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return
    
    print("✅ 项目结构检查通过")
    
    # 安装依赖
    if not install_dependencies():
        return
    
    # 启动服务器
    if not start_server():
        return
    
    print("👋 感谢使用智能聊天系统!")

if __name__ == "__main__":
    main()
