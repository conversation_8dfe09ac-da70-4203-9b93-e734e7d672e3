#!/usr/bin/env python3
"""
智能聊天系统测试脚本
验证AutoGen + DeepSeek配置和功能
"""

import sys
import json
from pathlib import Path

def test_imports():
    """测试依赖导入"""
    print("🔍 测试依赖导入...")
    
    # 测试基础依赖
    try:
        import fastapi
        print(f"✅ FastAPI: {fastapi.__version__}")
    except ImportError as e:
        print(f"❌ FastAPI导入失败: {e}")
        return False
    
    try:
        import uvicorn
        print(f"✅ Uvicorn: {uvicorn.__version__}")
    except ImportError as e:
        print(f"❌ Uvicorn导入失败: {e}")
        return False
    
    # 测试AutoGen
    try:
        import autogen
        print(f"✅ AutoGen: {autogen.__version__}")
        
        # 测试具体类导入
        from autogen import AssistantAgent, UserProxyAgent
        print("✅ AutoGen代理类导入成功")
        
    except ImportError as e:
        print(f"❌ AutoGen导入失败: {e}")
        print("请安装AutoGen: pip install pyautogen")
        return False
    
    # 测试OpenAI
    try:
        import openai
        print(f"✅ OpenAI: {openai.__version__}")
    except ImportError as e:
        print(f"❌ OpenAI导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置文件"""
    print("\n🔍 测试配置文件...")
    
    try:
        # 添加backend目录到路径
        backend_dir = Path(__file__).parent / "backend"
        sys.path.insert(0, str(backend_dir))
        
        from config import (
            DEEPSEEK_API_KEY, 
            DEEPSEEK_BASE_URL, 
            DEEPSEEK_MODEL,
            get_deepseek_config,
            validate_api_key
        )
        
        print(f"✅ API密钥: {'已配置' if validate_api_key() else '未配置'}")
        print(f"✅ 基础URL: {DEEPSEEK_BASE_URL}")
        print(f"✅ 模型: {DEEPSEEK_MODEL}")
        
        # 测试配置格式
        config = get_deepseek_config()
        print(f"✅ 配置格式: {json.dumps(config, indent=2, ensure_ascii=False)}")
        
        return validate_api_key()
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_chat_service():
    """测试聊天服务"""
    print("\n🔍 测试聊天服务...")
    
    try:
        # 添加backend目录到路径
        backend_dir = Path(__file__).parent / "backend"
        sys.path.insert(0, str(backend_dir))
        
        from chat_service import ChatService
        
        # 尝试初始化聊天服务
        chat_service = ChatService()
        print("✅ 聊天服务初始化成功")
        
        if chat_service.is_initialized:
            print("✅ AutoGen代理初始化成功")
            return True
        else:
            print("❌ AutoGen代理初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 聊天服务测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_api_connection():
    """测试API连接"""
    print("\n🔍 测试DeepSeek API连接...")
    
    try:
        import httpx
        
        # 添加backend目录到路径
        backend_dir = Path(__file__).parent / "backend"
        sys.path.insert(0, str(backend_dir))
        
        from config import DEEPSEEK_API_KEY, DEEPSEEK_BASE_URL
        
        # 测试API连接
        headers = {
            "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
            "Content-Type": "application/json"
        }
        
        # 简单的API测试请求
        test_data = {
            "model": "deepseek-chat",
            "messages": [{"role": "user", "content": "Hello"}],
            "max_tokens": 10
        }
        
        with httpx.Client(timeout=10.0) as client:
            response = client.post(
                f"{DEEPSEEK_BASE_URL}/chat/completions",
                headers=headers,
                json=test_data
            )
            
            if response.status_code == 200:
                print("✅ DeepSeek API连接成功")
                return True
            else:
                print(f"❌ API响应错误: {response.status_code} - {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🤖 智能聊天系统测试")
    print("=" * 40)
    
    # 检查Python版本
    print(f"🐍 Python版本: {sys.version}")
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return
    
    # 检查项目结构
    backend_dir = Path(__file__).parent / "backend"
    frontend_dir = Path(__file__).parent / "frontend"
    
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return
    
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return
    
    print("✅ 项目结构检查通过")
    
    # 运行测试
    tests = [
        ("依赖导入", test_imports),
        ("配置文件", test_config),
        ("聊天服务", test_chat_service),
        ("API连接", test_api_connection),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出测试结果
    print("\n" + "=" * 40)
    print("📊 测试结果汇总:")
    print("=" * 40)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("=" * 40)
    if all_passed:
        print("🎉 所有测试通过！系统可以正常启动。")
        print("💡 运行 'python start_server.py' 启动系统")
    else:
        print("⚠️ 部分测试失败，请检查配置和依赖。")
        print("💡 建议:")
        print("   1. 检查API密钥配置")
        print("   2. 安装缺失的依赖包")
        print("   3. 确认网络连接正常")
    
    print("=" * 40)

if __name__ == "__main__":
    main()
