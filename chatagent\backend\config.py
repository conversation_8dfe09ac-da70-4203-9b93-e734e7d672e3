"""
聊天系统配置文件
配置DeepSeek API和系统参数
"""

import os
from typing import Dict, Any

# DeepSeek API配置
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "***********************************")
DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1"
DEEPSEEK_MODEL = "deepseek-chat"

# 系统配置
SYSTEM_CONFIG = {
    "app_name": "智能聊天系统",
    "version": "1.0.0", 
    "description": "基于AutoGen + DeepSeek的智能聊天系统",
    "host": "0.0.0.0",
    "port": 8000,
    "debug": True
}

# AI配置
AI_CONFIG = {
    "temperature": 0.7,
    "max_tokens": 4000,
    "timeout": 120,
    "stream": True  # 启用流式输出
}

def get_deepseek_config() -> Dict[str, Any]:
    """获取DeepSeek配置 - AutoGen 0.5.7兼容格式"""
    return {
        "config_list": [
            {
                "model": DEEPSEEK_MODEL,
                "api_key": DEEPSEEK_API_KEY,
                "base_url": DEEPSEEK_BASE_URL,
                "api_type": "openai"
            }
        ],
        "temperature": AI_CONFIG["temperature"],
        "timeout": AI_CONFIG["timeout"],
        "cache_seed": None  # 禁用缓存，确保真实调用
    }

def validate_api_key() -> bool:
    """验证API密钥是否已设置"""
    return DEEPSEEK_API_KEY and len(DEEPSEEK_API_KEY) > 10

# CORS配置
CORS_CONFIG = {
    "allow_origins": ["*"],
    "allow_credentials": True,
    "allow_methods": ["*"],
    "allow_headers": ["*"],
}
