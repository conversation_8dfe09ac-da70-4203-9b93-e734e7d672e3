"""
智能聊天系统配置文件
负责配置DeepSeek API连接参数和系统运行参数
支持环境变量配置，提供默认值保证系统正常运行
"""

import os
from typing import Dict, Any

# DeepSeek API配置参数
# 从环境变量获取API密钥，如果未设置则使用默认值
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "***********************************")
# DeepSeek API的基础URL地址
DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1"
# 使用的DeepSeek模型名称
DEEPSEEK_MODEL = "deepseek-chat"

# 系统运行配置参数
SYSTEM_CONFIG = {
    "app_name": "智能聊天系统",  # 应用程序名称
    "version": "2.0.0",  # 版本号（包含增强功能）
    "description": "基于AutoGen + DeepSeek的智能聊天系统，支持文件上传、多轮对话、会话管理",
    "host": "0.0.0.0",  # 服务器监听地址，0.0.0.0表示监听所有网络接口
    "port": 8000,  # 服务器监听端口
    "debug": True  # 调试模式开关
}

# AI模型配置参数
AI_CONFIG = {
    "temperature": 0.7,  # 控制回复的随机性，0-1之间，越高越随机
    "max_tokens": 4000,  # 单次回复的最大token数量
    "timeout": 120,  # API请求超时时间（秒）
    "stream": True  # 启用流式输出，提供实时响应体验
}

def get_deepseek_config() -> Dict[str, Any]:
    """
    获取DeepSeek API配置信息
    返回AutoGen框架兼容的配置格式，用于初始化AI代理

    Returns:
        Dict[str, Any]: 包含API配置信息的字典
    """
    return {
        "config_list": [  # AutoGen要求的配置列表格式
            {
                "model": DEEPSEEK_MODEL,  # 使用的AI模型名称
                "api_key": DEEPSEEK_API_KEY,  # API访问密钥
                "base_url": DEEPSEEK_BASE_URL,  # API服务器地址
                "api_type": "openai"  # API类型，DeepSeek兼容OpenAI格式
            }
        ],
        "temperature": AI_CONFIG["temperature"],  # 回复随机性参数
        "timeout": AI_CONFIG["timeout"],  # 请求超时时间
        "cache_seed": None  # 禁用缓存，确保每次都是真实的API调用
    }

def validate_api_key() -> bool:
    """
    验证DeepSeek API密钥是否有效
    检查API密钥是否已设置且长度合理

    Returns:
        bool: True表示API密钥有效，False表示无效
    """
    return DEEPSEEK_API_KEY and len(DEEPSEEK_API_KEY) > 10

# 跨域资源共享(CORS)配置
# 允许前端从不同域名访问后端API
CORS_CONFIG = {
    "allow_origins": ["*"],  # 允许所有来源访问（生产环境建议限制具体域名）
    "allow_credentials": True,  # 允许携带认证信息
    "allow_methods": ["*"],  # 允许所有HTTP方法
    "allow_headers": ["*"],  # 允许所有请求头
}
