2025-06-10 07:35:50,355 - __main__ - INFO - ✅ 静态文件服务已配置
2025-06-10 07:36:43,198 - __main__ - INFO - ✅ 静态文件服务已配置
2025-06-10 07:42:11,046 - __main__ - INFO - ✅ 静态文件服务已配置
2025-06-10 07:42:28,515 - __main__ - INFO - ✅ 静态文件服务已配置
2025-06-10 11:06:55,777 - __main__ - INFO - 静态文件服务已配置
2025-06-10 11:25:23,248 - __main__ - INFO - 静态文件服务已配置
2025-06-10 11:26:16,187 - __main__ - INFO - 静态文件服务已配置
2025-06-10 11:26:46,807 - __main__ - INFO - 静态文件服务已配置
2025-06-10 11:31:30,512 - __main__ - INFO - 静态文件服务已配置
2025-06-10 11:31:30,512 - __main__ - INFO - 模板引擎已配置
2025-06-10 11:31:30,534 - __main__ - INFO - 准备启动服务器...
2025-06-10 11:31:30,534 - __main__ - INFO - 服务地址: http://0.0.0.0:8000
2025-06-10 11:31:30,534 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-06-10 11:31:30,534 - __main__ - INFO - 调试模式: 开启
2025-06-10 11:31:33,242 - __mp_main__ - INFO - 静态文件服务已配置
2025-06-10 11:31:33,243 - __mp_main__ - INFO - 模板引擎已配置
2025-06-10 11:31:33,306 - main - INFO - 静态文件服务已配置
2025-06-10 11:31:33,307 - main - INFO - 模板引擎已配置
2025-06-10 11:31:33,312 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 11:31:33,312 - main - INFO - 初始化系统配置...
2025-06-10 11:31:33,313 - main - INFO - 初始化数据库连接...
2025-06-10 11:31:33,313 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 11:31:33,380 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 11:31:33,404 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 11:31:33,404 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:31:33,405 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 11:31:33,406 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:31:33,406 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 11:31:33,406 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:31:33,407 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 11:31:33,408 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 11:31:33,408 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] ()
2025-06-10 11:31:33,408 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 11:31:33,408 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 11:31:33,409 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 11:31:33,409 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 11:31:57,736 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:31:57,741 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 11:31:57,742 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:31:57,748 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 11:31:57,750 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 11:31:57,750 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 11:31:57,750 - main - INFO - 检查AI服务状态...
2025-06-10 11:31:57,750 - main - INFO - AI服务就绪
2025-06-10 11:31:57,750 - main - INFO - 系统启动完成
2025-06-10 11:31:57,750 - main - INFO - 系统信息:
2025-06-10 11:31:57,750 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 11:31:57,750 - main - INFO -   - 版本: 2.0.0
2025-06-10 11:31:57,750 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 11:31:57,750 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 11:31:57,750 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 11:31:57,750 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 11:34:07,302 - main - INFO - 静态文件服务已配置
2025-06-10 11:34:07,306 - main - INFO - 模板引擎已配置
2025-06-10 11:34:07,322 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 11:34:07,323 - main - INFO - 初始化系统配置...
2025-06-10 11:34:07,337 - main - INFO - 初始化数据库连接...
2025-06-10 11:34:07,337 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 11:34:07,419 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 11:34:07,436 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 11:34:07,437 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:34:07,439 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 11:34:07,439 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:34:07,441 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 11:34:07,441 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:34:07,442 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 11:34:07,443 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 11:34:07,449 - sqlalchemy.engine.Engine - INFO - [generated in 0.00564s] ()
2025-06-10 11:34:07,450 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 11:34:07,450 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 11:34:07,452 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 11:34:07,452 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 11:34:07,453 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:34:07,455 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 11:34:07,456 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:34:07,458 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 11:34:07,462 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 11:34:07,466 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 11:34:07,467 - main - INFO - 检查AI服务状态...
2025-06-10 11:34:07,467 - main - INFO - AI服务就绪
2025-06-10 11:34:07,468 - main - INFO - 系统启动完成
2025-06-10 11:34:07,468 - main - INFO - 系统信息:
2025-06-10 11:34:07,468 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 11:34:07,468 - main - INFO -   - 版本: 2.0.0
2025-06-10 11:34:07,469 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 11:34:07,470 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 11:34:07,470 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 11:34:07,471 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 11:35:07,826 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 11:35:07,828 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 11:35:07,836 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 11:35:07,836 - sqlalchemy.engine.Engine - INFO - [generated in 0.00044s] ()
2025-06-10 11:35:07,841 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 11:35:07,841 - sqlalchemy.engine.Engine - INFO - [generated in 0.00028s] (0, 20)
2025-06-10 11:35:07,842 - app.services.requirement_service - ERROR -  获取需求列表失败: (pymysql.err.OperationalError) (1054, "Unknown column 'requirements.modules' in 'field list'")
[SQL: SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s]
[parameters: (0, 20)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-10 11:35:07,842 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 11:35:07,842 - app.database.connection - ERROR - 异步数据库会话错误: (pymysql.err.OperationalError) (1054, "Unknown column 'requirements.modules' in 'field list'")
[SQL: SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s]
[parameters: (0, 20)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-10 11:35:07,843 - main - ERROR - 未处理的异常: 'NoneType' object has no attribute 'HTTP_500_INTERNAL_SERVER_ERROR'
Traceback (most recent call last):
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\dialects\mysql\aiomysql.py", line 97, in execute
    return self.await_(self._execute_async(operation, parameters))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\dialects\mysql\aiomysql.py", line 106, in _execute_async
    result = await self._cursor.execute(operation, parameters)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\cursors.py", line 239, in execute
    await self._query(query)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\cursors.py", line 457, in _query
    await conn.query(q)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\connection.py", line 469, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\connection.py", line 683, in _read_query_result
    await result.read()
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\connection.py", line 1164, in read
    first_packet = await self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\connection.py", line 652, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'requirements.modules' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Project\Python_project\Project03\Project03\data_platform_test_system\backend\app\api\v1\requirements.py", line 175, in get_requirements
    requirements, total = await requirement_service.get_requirements_list(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "D:\Project\Python_project\Project03\Project03\data_platform_test_system\backend\app\services\requirement_service.py", line 178, in get_requirements_list
    result = await session.execute(stmt)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 463, in execute
    result = await greenlet_spawn(
             ^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\dialects\mysql\aiomysql.py", line 97, in execute
    return self.await_(self._execute_async(operation, parameters))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\dialects\mysql\aiomysql.py", line 106, in _execute_async
    result = await self._cursor.execute(operation, parameters)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\cursors.py", line 239, in execute
    await self._query(query)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\cursors.py", line 457, in _query
    await conn.query(q)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\connection.py", line 469, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\connection.py", line 683, in _read_query_result
    await result.read()
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\connection.py", line 1164, in read
    first_packet = await self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\connection.py", line 652, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'requirements.modules' in 'field list'")
[SQL: SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s]
[parameters: (0, 20)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\Project03\data_platform_test_system\backend\app\api\v1\requirements.py", line 214, in get_requirements
    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'HTTP_500_INTERNAL_SERVER_ERROR'
2025-06-10 11:35:29,866 - app.services.requirement_service - INFO -  获取分析结果列表: 页码=1, 大小=20
2025-06-10 11:35:29,872 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 11:35:29,872 - sqlalchemy.engine.Engine - INFO - SELECT count(ai_analysis_results.id) AS count_1 
FROM ai_analysis_results
2025-06-10 11:35:29,872 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] ()
2025-06-10 11:35:29,877 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.id, ai_analysis_results.req_id, ai_analysis_results.analysis_result, ai_analysis_results.structured_result, ai_analysis_results.key_points, ai_analysis_results.risk_assessment, ai_analysis_results.suggestions, ai_analysis_results.manual_adjust, ai_analysis_results.is_manually_adjusted, ai_analysis_results.adjusted_by, ai_analysis_results.adjusted_time, ai_analysis_results.ai_model, ai_analysis_results.ai_version, ai_analysis_results.analysis_config, ai_analysis_results.analysis_status, ai_analysis_results.error_message, ai_analysis_results.processing_time, ai_analysis_results.created_time, ai_analysis_results.update_time 
FROM ai_analysis_results ORDER BY ai_analysis_results.created_time DESC 
 LIMIT %s, %s
2025-06-10 11:35:29,877 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] (0, 20)
2025-06-10 11:35:29,878 - app.services.requirement_service - INFO -  获取分析结果列表成功: 返回0条记录，总数=0
2025-06-10 11:35:29,878 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 11:37:42,183 - main - INFO - 正在关闭系统...
2025-06-10 11:37:42,184 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 11:37:42,184 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 11:37:42,185 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 11:37:42,185 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 11:37:42,185 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 11:37:42,185 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 11:37:42,185 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 11:37:42,185 - main - INFO - 数据库连接已关闭
2025-06-10 11:37:42,186 - main - INFO - 系统已关闭
2025-06-10 11:37:42,217 - main - INFO - 正在关闭系统...
2025-06-10 11:37:42,218 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 11:37:42,243 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 11:37:42,250 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 11:37:42,250 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 11:37:42,252 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 11:37:42,255 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 11:37:42,255 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 11:37:42,256 - main - INFO - 数据库连接已关闭
2025-06-10 11:37:42,256 - main - INFO - 系统已关闭
2025-06-10 12:00:42,080 - main - INFO - 静态文件服务已配置
2025-06-10 12:00:42,080 - main - INFO - 模板引擎已配置
2025-06-10 12:00:42,090 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 12:00:42,090 - main - INFO - 初始化系统配置...
2025-06-10 12:00:42,106 - main - INFO - 初始化数据库连接...
2025-06-10 12:00:42,106 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 12:00:42,189 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 12:00:42,206 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 12:00:42,208 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:00:42,209 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 12:00:42,209 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:00:42,210 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 12:00:42,216 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:00:42,217 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 12:00:42,218 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 12:00:42,218 - sqlalchemy.engine.Engine - INFO - [generated in 0.00042s] ()
2025-06-10 12:00:42,219 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 12:00:42,219 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 12:00:42,221 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 12:00:42,225 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 12:00:42,225 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:00:42,229 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 12:00:42,230 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:00:42,236 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 12:00:42,239 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 12:00:42,239 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 12:00:42,239 - main - INFO - 检查AI服务状态...
2025-06-10 12:00:42,240 - main - INFO - AI服务就绪
2025-06-10 12:00:42,240 - main - INFO - 系统启动完成
2025-06-10 12:00:42,240 - main - INFO - 系统信息:
2025-06-10 12:00:42,240 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 12:00:42,240 - main - INFO -   - 版本: 2.0.0
2025-06-10 12:00:42,240 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 12:00:42,240 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 12:00:42,241 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 12:00:42,241 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 12:42:31,087 - main - INFO - 正在关闭系统...
2025-06-10 12:42:31,088 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 12:42:31,089 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 12:42:31,090 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 12:42:31,091 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 12:42:31,092 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 12:42:31,093 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 12:42:31,093 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 12:42:31,094 - main - INFO - 数据库连接已关闭
2025-06-10 12:42:31,095 - main - INFO - 系统已关闭
2025-06-10 12:43:32,070 - main - INFO - 静态文件服务已配置
2025-06-10 12:43:32,070 - main - INFO - 模板引擎已配置
2025-06-10 12:43:32,079 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 12:43:32,080 - main - INFO - 初始化系统配置...
2025-06-10 12:43:32,083 - main - INFO - 初始化数据库连接...
2025-06-10 12:43:32,083 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 12:43:32,285 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 12:43:32,826 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 12:43:32,827 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:43:32,938 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 12:43:32,940 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:43:32,941 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 12:43:32,942 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:43:32,943 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 12:43:32,944 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 12:43:32,945 - sqlalchemy.engine.Engine - INFO - [generated in 0.00075s] ()
2025-06-10 12:43:32,946 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 12:43:32,957 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 12:43:32,959 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 12:43:32,960 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 12:43:32,961 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:43:32,964 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 12:43:32,965 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:43:32,968 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 12:43:32,975 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 12:43:32,975 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 12:43:32,976 - main - INFO - 检查AI服务状态...
2025-06-10 12:43:32,976 - main - INFO - AI服务就绪
2025-06-10 12:43:32,976 - main - INFO - 系统启动完成
2025-06-10 12:43:32,977 - main - INFO - 系统信息:
2025-06-10 12:43:32,977 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 12:43:32,977 - main - INFO -   - 版本: 2.0.0
2025-06-10 12:43:32,978 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 12:43:32,978 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 12:43:32,978 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 12:43:32,978 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 12:55:55,961 - main - INFO - 正在关闭系统...
2025-06-10 12:55:55,962 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 12:55:56,042 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 12:55:56,093 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 12:55:56,103 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 12:55:56,124 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 12:55:56,142 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 12:55:56,145 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 12:55:56,147 - main - INFO - 数据库连接已关闭
2025-06-10 12:55:56,148 - main - INFO - 系统已关闭
2025-06-10 12:56:04,214 - main - INFO - 静态文件服务已配置
2025-06-10 12:56:04,215 - main - INFO - 模板引擎已配置
2025-06-10 12:56:04,225 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 12:56:04,226 - main - INFO - 初始化系统配置...
2025-06-10 12:56:04,231 - main - INFO - 初始化数据库连接...
2025-06-10 12:56:04,232 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 12:56:04,330 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 12:56:04,350 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 12:56:04,350 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:56:04,352 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 12:56:04,352 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:56:04,356 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 12:56:04,359 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:56:04,361 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 12:56:04,362 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 12:56:04,363 - sqlalchemy.engine.Engine - INFO - [generated in 0.00058s] ()
2025-06-10 12:56:04,364 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 12:56:04,364 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 12:56:04,366 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 12:56:04,366 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 12:56:04,367 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:56:04,370 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 12:56:04,374 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:56:04,379 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 12:56:04,380 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 12:56:04,381 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 12:56:04,381 - main - INFO - 检查AI服务状态...
2025-06-10 12:56:04,381 - main - INFO - AI服务就绪
2025-06-10 12:56:04,382 - main - INFO - 系统启动完成
2025-06-10 12:56:04,382 - main - INFO - 系统信息:
2025-06-10 12:56:04,382 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 12:56:04,382 - main - INFO -   - 版本: 2.0.0
2025-06-10 12:56:04,382 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 12:56:04,382 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 12:56:04,383 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 12:56:04,383 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 15:33:10,716 - main - INFO - 正在关闭系统...
2025-06-10 15:33:10,720 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 15:33:10,733 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 15:33:10,735 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 15:33:10,735 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 15:33:10,736 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 15:33:10,736 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 15:33:10,740 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 15:33:10,741 - main - INFO - 数据库连接已关闭
2025-06-10 15:33:10,741 - main - INFO - 系统已关闭
2025-06-10 15:33:23,641 - main - INFO - 静态文件服务已配置
2025-06-10 15:33:23,791 - main - INFO - 模板引擎已配置
2025-06-10 15:33:23,824 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 15:33:23,824 - main - INFO - 初始化系统配置...
2025-06-10 15:33:23,828 - main - INFO - 初始化数据库连接...
2025-06-10 15:33:23,829 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 15:33:23,931 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 15:33:23,965 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 15:33:23,968 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 15:33:23,970 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 15:33:23,973 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 15:33:23,976 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 15:33:23,977 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 15:33:23,979 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 15:33:23,986 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 15:33:23,986 - sqlalchemy.engine.Engine - INFO - [generated in 0.00062s] ()
2025-06-10 15:33:23,989 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 15:33:23,992 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 15:33:23,995 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 15:33:24,002 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 15:33:24,005 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 15:33:24,011 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 15:33:24,017 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 15:33:24,023 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 15:33:24,028 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 15:33:24,028 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 15:33:24,028 - main - INFO - 检查AI服务状态...
2025-06-10 15:33:24,037 - main - INFO - AI服务就绪
2025-06-10 15:33:24,038 - main - INFO - 系统启动完成
2025-06-10 15:33:24,038 - main - INFO - 系统信息:
2025-06-10 15:33:24,038 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 15:33:24,038 - main - INFO -   - 版本: 2.0.0
2025-06-10 15:33:24,039 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 15:33:24,040 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 15:33:24,040 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 15:33:24,040 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 15:42:09,152 - main - INFO - 正在关闭系统...
2025-06-10 15:42:09,153 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 15:42:09,157 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 15:42:09,157 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 15:42:09,158 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 15:42:09,158 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 15:42:09,159 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 15:42:09,159 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 15:42:09,159 - main - INFO - 数据库连接已关闭
2025-06-10 15:42:09,159 - main - INFO - 系统已关闭
2025-06-10 15:48:31,530 - main - INFO - 静态文件服务已配置
2025-06-10 15:48:31,531 - main - INFO - 模板引擎已配置
2025-06-10 15:48:31,541 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 15:48:31,542 - main - INFO - 初始化系统配置...
2025-06-10 15:48:31,545 - main - INFO - 初始化数据库连接...
2025-06-10 15:48:31,547 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 15:48:31,639 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 15:48:31,798 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 15:48:31,799 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 15:48:31,801 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 15:48:31,805 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 15:48:31,807 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 15:48:31,807 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 15:48:31,810 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 15:48:31,811 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 15:48:31,813 - sqlalchemy.engine.Engine - INFO - [generated in 0.00199s] ()
2025-06-10 15:48:31,816 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 15:48:31,816 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 15:48:31,821 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 15:48:31,822 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 15:48:31,822 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 15:48:31,825 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 15:48:31,825 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 15:48:31,828 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 15:48:31,832 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 15:48:31,836 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 15:48:31,838 - main - INFO - 检查AI服务状态...
2025-06-10 15:48:31,838 - main - INFO - AI服务就绪
2025-06-10 15:48:31,839 - main - INFO - 系统启动完成
2025-06-10 15:48:31,839 - main - INFO - 系统信息:
2025-06-10 15:48:31,839 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 15:48:31,840 - main - INFO -   - 版本: 2.0.0
2025-06-10 15:48:31,840 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 15:48:31,840 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 15:48:31,840 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 15:48:31,840 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 15:53:58,218 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 15:53:58,259 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 15:53:58,266 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 15:53:58,276 - sqlalchemy.engine.Engine - INFO - [generated in 0.00956s] ()
2025-06-10 15:53:58,280 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 15:53:58,284 - sqlalchemy.engine.Engine - INFO - [generated in 0.00415s] (0, 20)
2025-06-10 15:53:58,296 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 15:53:58,314 - sqlalchemy.engine.Engine - INFO - [generated in 0.01863s] (1, 2, 3, 4)
2025-06-10 15:53:58,319 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 15:53:58,343 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 15:54:23,515 - app.services.requirement_service - INFO -  获取分析结果列表: 页码=1, 大小=20
2025-06-10 15:54:23,519 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 15:54:23,520 - sqlalchemy.engine.Engine - INFO - SELECT count(ai_analysis_results.id) AS count_1 
FROM ai_analysis_results
2025-06-10 15:54:23,526 - sqlalchemy.engine.Engine - INFO - [generated in 0.00575s] ()
2025-06-10 15:54:23,534 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.id, ai_analysis_results.req_id, ai_analysis_results.analysis_result, ai_analysis_results.structured_result, ai_analysis_results.key_points, ai_analysis_results.risk_assessment, ai_analysis_results.suggestions, ai_analysis_results.manual_adjust, ai_analysis_results.is_manually_adjusted, ai_analysis_results.adjusted_by, ai_analysis_results.adjusted_time, ai_analysis_results.ai_model, ai_analysis_results.ai_version, ai_analysis_results.analysis_config, ai_analysis_results.analysis_status, ai_analysis_results.error_message, ai_analysis_results.processing_time, ai_analysis_results.created_time, ai_analysis_results.update_time 
FROM ai_analysis_results ORDER BY ai_analysis_results.created_time DESC 
 LIMIT %s, %s
2025-06-10 15:54:23,537 - sqlalchemy.engine.Engine - INFO - [generated in 0.00310s] (0, 20)
2025-06-10 15:54:23,540 - app.services.requirement_service - INFO -  获取分析结果列表成功: 返回0条记录，总数=0
2025-06-10 15:54:23,553 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 16:23:20,987 - main - INFO - 正在关闭系统...
2025-06-10 16:23:20,987 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 16:23:20,988 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 16:23:21,052 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 16:23:21,057 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 16:23:21,059 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 16:23:21,061 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 16:23:21,061 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 16:23:21,062 - main - INFO - 数据库连接已关闭
2025-06-10 16:23:21,071 - main - INFO - 系统已关闭
2025-06-10 16:23:36,114 - main - INFO - 静态文件服务已配置
2025-06-10 16:23:36,277 - main - INFO - 模板引擎已配置
2025-06-10 16:23:38,718 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 16:23:38,718 - main - INFO - 初始化系统配置...
2025-06-10 16:23:38,721 - main - INFO - 初始化数据库连接...
2025-06-10 16:23:38,726 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 16:23:39,653 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 16:23:41,382 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 16:23:41,815 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 16:23:41,817 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 16:23:42,663 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 16:23:43,579 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 16:23:43,580 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 16:23:43,582 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:23:43,583 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 16:23:43,586 - sqlalchemy.engine.Engine - INFO - [generated in 0.00345s] ()
2025-06-10 16:23:43,587 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 16:23:43,588 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 16:23:43,589 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:23:43,589 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 16:23:43,590 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 16:23:43,593 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 16:23:43,593 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 16:23:43,596 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 16:23:43,597 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 16:23:43,597 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 16:23:43,598 - main - INFO - 检查AI服务状态...
2025-06-10 16:23:43,598 - main - INFO - AI服务就绪
2025-06-10 16:23:43,598 - main - INFO - 系统启动完成
2025-06-10 16:23:43,598 - main - INFO - 系统信息:
2025-06-10 16:23:43,598 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 16:23:43,598 - main - INFO -   - 版本: 2.0.0
2025-06-10 16:23:43,599 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 16:23:43,599 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 16:23:43,599 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 16:23:43,599 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 16:23:55,648 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:23:55,653 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 16:23:55,660 - sqlalchemy.engine.Engine - INFO - [cached since 12.08s ago] ()
2025-06-10 16:23:55,664 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 16:23:55,665 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 16:47:14,624 - main - INFO - 正在关闭系统...
2025-06-10 16:47:14,624 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 16:47:14,626 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 16:47:14,628 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 16:47:14,628 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 16:47:14,629 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 16:47:14,629 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 16:47:14,629 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 16:47:14,634 - main - INFO - 数据库连接已关闭
2025-06-10 16:47:14,634 - main - INFO - 系统已关闭
2025-06-10 16:47:26,719 - main - INFO - 静态文件服务已配置
2025-06-10 16:47:26,721 - main - INFO - 模板引擎已配置
2025-06-10 16:47:26,735 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 16:47:26,735 - main - INFO - 初始化系统配置...
2025-06-10 16:47:26,738 - main - INFO - 初始化数据库连接...
2025-06-10 16:47:26,738 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 16:47:26,860 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 16:47:26,888 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 16:47:26,895 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 16:47:26,903 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 16:47:26,904 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 16:47:26,917 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 16:47:26,918 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 16:47:26,920 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:47:26,921 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 16:47:26,931 - sqlalchemy.engine.Engine - INFO - [generated in 0.01072s] ()
2025-06-10 16:47:26,934 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 16:47:26,934 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 16:47:26,935 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:47:26,936 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 16:47:26,937 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 16:47:26,946 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 16:47:26,947 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 16:47:26,951 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 16:47:26,953 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 16:47:26,954 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 16:47:26,954 - main - INFO - 检查AI服务状态...
2025-06-10 16:47:26,963 - main - INFO - AI服务就绪
2025-06-10 16:47:26,967 - main - INFO - 系统启动完成
2025-06-10 16:47:26,967 - main - INFO - 系统信息:
2025-06-10 16:47:26,968 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 16:47:26,968 - main - INFO -   - 版本: 2.0.0
2025-06-10 16:47:26,968 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 16:47:26,968 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 16:47:26,969 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 16:47:26,969 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 16:49:33,964 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=1
2025-06-10 16:49:33,965 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:49:33,973 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 16:49:33,978 - sqlalchemy.engine.Engine - INFO - [generated in 0.00469s] ()
2025-06-10 16:49:33,985 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 16:49:33,988 - sqlalchemy.engine.Engine - INFO - [generated in 0.00313s] (0, 1)
2025-06-10 16:49:33,992 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s)
2025-06-10 16:49:33,993 - sqlalchemy.engine.Engine - INFO - [generated in 0.00090s] (4,)
2025-06-10 16:49:33,996 - app.services.requirement_service - INFO -  获取需求列表成功: 返回1条记录，总数=4
2025-06-10 16:49:33,996 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 16:49:34,011 - app.services.requirement_service - INFO -  获取分析结果列表: 页码=1, 大小=1
2025-06-10 16:49:34,014 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:49:34,059 - sqlalchemy.engine.Engine - INFO - SELECT count(ai_analysis_results.id) AS count_1 
FROM ai_analysis_results
2025-06-10 16:49:34,069 - sqlalchemy.engine.Engine - INFO - [generated in 0.01068s] ()
2025-06-10 16:49:34,073 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.id, ai_analysis_results.req_id, ai_analysis_results.analysis_result, ai_analysis_results.structured_result, ai_analysis_results.key_points, ai_analysis_results.risk_assessment, ai_analysis_results.suggestions, ai_analysis_results.manual_adjust, ai_analysis_results.is_manually_adjusted, ai_analysis_results.adjusted_by, ai_analysis_results.adjusted_time, ai_analysis_results.ai_model, ai_analysis_results.ai_version, ai_analysis_results.analysis_config, ai_analysis_results.analysis_status, ai_analysis_results.error_message, ai_analysis_results.processing_time, ai_analysis_results.created_time, ai_analysis_results.update_time 
FROM ai_analysis_results ORDER BY ai_analysis_results.created_time DESC 
 LIMIT %s, %s
2025-06-10 16:49:34,074 - sqlalchemy.engine.Engine - INFO - [generated in 0.00140s] (0, 1)
2025-06-10 16:49:34,079 - app.services.requirement_service - INFO -  获取分析结果列表成功: 返回0条记录，总数=0
2025-06-10 16:49:34,084 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 16:49:34,099 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:49:34,100 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 16:49:34,105 - sqlalchemy.engine.Engine - INFO - [cached since 127.2s ago] ()
2025-06-10 16:49:34,108 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 16:49:34,110 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 16:49:35,617 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=1
2025-06-10 16:49:35,656 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:49:35,658 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 16:49:35,659 - sqlalchemy.engine.Engine - INFO - [cached since 1.685s ago] ()
2025-06-10 16:49:35,661 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 16:49:35,662 - sqlalchemy.engine.Engine - INFO - [cached since 1.677s ago] (0, 1)
2025-06-10 16:49:35,665 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s)
2025-06-10 16:49:35,721 - sqlalchemy.engine.Engine - INFO - [cached since 1.73s ago] (4,)
2025-06-10 16:49:35,747 - app.services.requirement_service - INFO -  获取需求列表成功: 返回1条记录，总数=4
2025-06-10 16:49:35,751 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 16:49:35,760 - app.services.requirement_service - INFO -  获取分析结果列表: 页码=1, 大小=1
2025-06-10 16:49:35,761 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:49:35,762 - sqlalchemy.engine.Engine - INFO - SELECT count(ai_analysis_results.id) AS count_1 
FROM ai_analysis_results
2025-06-10 16:49:35,763 - sqlalchemy.engine.Engine - INFO - [cached since 1.704s ago] ()
2025-06-10 16:49:35,768 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.id, ai_analysis_results.req_id, ai_analysis_results.analysis_result, ai_analysis_results.structured_result, ai_analysis_results.key_points, ai_analysis_results.risk_assessment, ai_analysis_results.suggestions, ai_analysis_results.manual_adjust, ai_analysis_results.is_manually_adjusted, ai_analysis_results.adjusted_by, ai_analysis_results.adjusted_time, ai_analysis_results.ai_model, ai_analysis_results.ai_version, ai_analysis_results.analysis_config, ai_analysis_results.analysis_status, ai_analysis_results.error_message, ai_analysis_results.processing_time, ai_analysis_results.created_time, ai_analysis_results.update_time 
FROM ai_analysis_results ORDER BY ai_analysis_results.created_time DESC 
 LIMIT %s, %s
2025-06-10 16:49:35,771 - sqlalchemy.engine.Engine - INFO - [cached since 1.698s ago] (0, 1)
2025-06-10 16:49:35,772 - app.services.requirement_service - INFO -  获取分析结果列表成功: 返回0条记录，总数=0
2025-06-10 16:49:35,773 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 16:49:35,780 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:49:35,784 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 16:49:35,785 - sqlalchemy.engine.Engine - INFO - [cached since 128.9s ago] ()
2025-06-10 16:49:35,786 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 16:49:35,787 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 16:49:41,461 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 16:49:41,490 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:49:41,557 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 16:49:41,589 - sqlalchemy.engine.Engine - INFO - [cached since 7.615s ago] ()
2025-06-10 16:49:41,676 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 16:49:41,821 - sqlalchemy.engine.Engine - INFO - [cached since 7.837s ago] (0, 20)
2025-06-10 16:49:41,840 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 16:49:41,940 - sqlalchemy.engine.Engine - INFO - [cached since 7.949s ago] (1, 2, 3, 4)
2025-06-10 16:49:41,944 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 16:49:41,944 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 16:49:53,992 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 16:49:53,993 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:49:53,993 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 16:49:53,994 - sqlalchemy.engine.Engine - INFO - [cached since 20.02s ago] ()
2025-06-10 16:49:53,996 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 16:49:53,996 - sqlalchemy.engine.Engine - INFO - [cached since 20.01s ago] (0, 20)
2025-06-10 16:49:53,998 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 16:49:54,002 - sqlalchemy.engine.Engine - INFO - [cached since 20.01s ago] (1, 2, 3, 4)
2025-06-10 16:49:54,011 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 16:49:54,011 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 16:50:08,718 - app.services.requirement_service - INFO -  获取分析结果列表: 页码=1, 大小=20
2025-06-10 16:50:08,720 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:50:08,721 - sqlalchemy.engine.Engine - INFO - SELECT count(ai_analysis_results.id) AS count_1 
FROM ai_analysis_results
2025-06-10 16:50:08,722 - sqlalchemy.engine.Engine - INFO - [cached since 34.66s ago] ()
2025-06-10 16:50:08,723 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.id, ai_analysis_results.req_id, ai_analysis_results.analysis_result, ai_analysis_results.structured_result, ai_analysis_results.key_points, ai_analysis_results.risk_assessment, ai_analysis_results.suggestions, ai_analysis_results.manual_adjust, ai_analysis_results.is_manually_adjusted, ai_analysis_results.adjusted_by, ai_analysis_results.adjusted_time, ai_analysis_results.ai_model, ai_analysis_results.ai_version, ai_analysis_results.analysis_config, ai_analysis_results.analysis_status, ai_analysis_results.error_message, ai_analysis_results.processing_time, ai_analysis_results.created_time, ai_analysis_results.update_time 
FROM ai_analysis_results ORDER BY ai_analysis_results.created_time DESC 
 LIMIT %s, %s
2025-06-10 16:50:08,724 - sqlalchemy.engine.Engine - INFO - [cached since 34.65s ago] (0, 20)
2025-06-10 16:50:08,725 - app.services.requirement_service - INFO -  获取分析结果列表成功: 返回0条记录，总数=0
2025-06-10 16:50:08,730 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 16:50:23,478 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=1
2025-06-10 16:50:23,512 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:50:23,513 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 16:50:23,514 - sqlalchemy.engine.Engine - INFO - [cached since 49.54s ago] ()
2025-06-10 16:50:23,518 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 16:50:23,527 - sqlalchemy.engine.Engine - INFO - [cached since 49.54s ago] (0, 1)
2025-06-10 16:50:23,530 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s)
2025-06-10 16:50:23,545 - sqlalchemy.engine.Engine - INFO - [cached since 49.55s ago] (4,)
2025-06-10 16:50:23,547 - app.services.requirement_service - INFO -  获取需求列表成功: 返回1条记录，总数=4
2025-06-10 16:50:23,547 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 16:50:23,646 - app.services.requirement_service - INFO -  获取分析结果列表: 页码=1, 大小=1
2025-06-10 16:50:23,650 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:50:23,654 - sqlalchemy.engine.Engine - INFO - SELECT count(ai_analysis_results.id) AS count_1 
FROM ai_analysis_results
2025-06-10 16:50:23,662 - sqlalchemy.engine.Engine - INFO - [cached since 49.6s ago] ()
2025-06-10 16:50:23,664 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.id, ai_analysis_results.req_id, ai_analysis_results.analysis_result, ai_analysis_results.structured_result, ai_analysis_results.key_points, ai_analysis_results.risk_assessment, ai_analysis_results.suggestions, ai_analysis_results.manual_adjust, ai_analysis_results.is_manually_adjusted, ai_analysis_results.adjusted_by, ai_analysis_results.adjusted_time, ai_analysis_results.ai_model, ai_analysis_results.ai_version, ai_analysis_results.analysis_config, ai_analysis_results.analysis_status, ai_analysis_results.error_message, ai_analysis_results.processing_time, ai_analysis_results.created_time, ai_analysis_results.update_time 
FROM ai_analysis_results ORDER BY ai_analysis_results.created_time DESC 
 LIMIT %s, %s
2025-06-10 16:50:23,666 - sqlalchemy.engine.Engine - INFO - [cached since 49.59s ago] (0, 1)
2025-06-10 16:50:23,672 - app.services.requirement_service - INFO -  获取分析结果列表成功: 返回0条记录，总数=0
2025-06-10 16:50:23,678 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 16:50:23,697 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 16:50:23,808 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 16:50:23,808 - sqlalchemy.engine.Engine - INFO - [cached since 176.9s ago] ()
2025-06-10 16:50:23,809 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 16:50:23,810 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 17:20:52,403 - main - INFO - 正在关闭系统...
2025-06-10 17:20:52,404 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 17:20:52,405 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 17:20:52,406 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 17:20:52,406 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 17:20:52,407 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 17:20:52,407 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 17:20:52,407 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 17:20:52,409 - main - INFO - 数据库连接已关闭
2025-06-10 17:20:52,409 - main - INFO - 系统已关闭
2025-06-10 17:21:06,827 - main - INFO - 静态文件服务已配置
2025-06-10 17:21:06,832 - main - INFO - 模板引擎已配置
2025-06-10 17:21:06,848 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 17:21:06,849 - main - INFO - 初始化系统配置...
2025-06-10 17:21:06,852 - main - INFO - 初始化数据库连接...
2025-06-10 17:21:06,857 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 17:21:06,953 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 17:21:06,985 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 17:21:06,993 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 17:21:06,995 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 17:21:06,996 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 17:21:06,997 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 17:21:07,017 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 17:21:07,024 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 17:21:07,028 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 17:21:07,028 - sqlalchemy.engine.Engine - INFO - [generated in 0.00070s] ()
2025-06-10 17:21:07,030 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 17:21:07,030 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 17:21:07,033 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 17:21:07,044 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 17:21:07,045 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 17:21:07,048 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 17:21:07,049 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 17:21:07,053 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 17:21:07,062 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 17:21:07,063 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 17:21:07,063 - main - INFO - 检查AI服务状态...
2025-06-10 17:21:07,063 - main - INFO - AI服务就绪
2025-06-10 17:21:07,063 - main - INFO - 系统启动完成
2025-06-10 17:21:07,064 - main - INFO - 系统信息:
2025-06-10 17:21:07,064 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 17:21:07,064 - main - INFO -   - 版本: 2.0.0
2025-06-10 17:21:07,064 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 17:21:07,064 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 17:21:07,065 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 17:21:07,065 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 17:55:55,955 - main - INFO - 正在关闭系统...
2025-06-10 17:55:55,957 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 17:55:55,958 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 17:55:55,958 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 17:55:55,959 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 17:55:55,961 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 17:55:55,963 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 17:55:55,966 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 17:55:55,967 - main - INFO - 数据库连接已关闭
2025-06-10 17:55:55,967 - main - INFO - 系统已关闭
2025-06-10 17:56:10,836 - main - INFO - 静态文件服务已配置
2025-06-10 17:56:10,838 - main - INFO - 模板引擎已配置
2025-06-10 17:56:10,918 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 17:56:10,919 - main - INFO - 初始化系统配置...
2025-06-10 17:56:11,272 - main - INFO - 初始化数据库连接...
2025-06-10 17:56:11,284 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 17:56:11,421 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 17:56:11,835 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 17:56:11,888 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 17:56:11,896 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 17:56:11,898 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 17:56:11,900 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 17:56:11,901 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 17:56:11,903 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 17:56:11,904 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 17:56:11,904 - sqlalchemy.engine.Engine - INFO - [generated in 0.00046s] ()
2025-06-10 17:56:11,919 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 17:56:11,920 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 17:56:12,036 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 17:56:12,050 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 17:56:12,053 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 17:56:12,066 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 17:56:12,067 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 17:56:12,070 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 17:56:12,083 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 17:56:12,084 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 17:56:12,085 - main - INFO - 检查AI服务状态...
2025-06-10 17:56:12,085 - main - INFO - AI服务就绪
2025-06-10 17:56:12,085 - main - INFO - 系统启动完成
2025-06-10 17:56:12,086 - main - INFO - 系统信息:
2025-06-10 17:56:12,086 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 17:56:12,086 - main - INFO -   - 版本: 2.0.0
2025-06-10 17:56:12,086 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 17:56:12,087 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 17:56:12,087 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 17:56:12,087 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 18:05:07,551 - main - INFO - 静态文件服务已配置
2025-06-10 18:05:07,552 - main - INFO - 模板引擎已配置
2025-06-10 18:05:07,571 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 18:05:07,573 - main - INFO - 初始化系统配置...
2025-06-10 18:05:07,770 - main - INFO - 初始化数据库连接...
2025-06-10 18:05:07,770 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 18:05:07,884 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 18:05:07,986 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 18:05:08,001 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:05:08,003 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 18:05:08,003 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:05:08,005 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 18:05:08,019 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:05:08,021 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:05:08,022 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 18:05:08,023 - sqlalchemy.engine.Engine - INFO - [generated in 0.00065s] ()
2025-06-10 18:05:08,028 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 18:05:08,028 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 18:05:08,034 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:05:08,038 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 18:05:08,039 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:05:08,054 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 18:05:08,055 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:05:08,068 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 18:05:08,071 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 18:05:08,073 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 18:05:08,073 - main - INFO - 检查AI服务状态...
2025-06-10 18:05:08,074 - main - INFO - AI服务就绪
2025-06-10 18:05:08,074 - main - INFO - 系统启动完成
2025-06-10 18:05:08,074 - main - INFO - 系统信息:
2025-06-10 18:05:08,074 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 18:05:08,074 - main - INFO -   - 版本: 2.0.0
2025-06-10 18:05:08,077 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 18:05:08,078 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 18:05:08,083 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 18:05:08,084 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 18:15:06,192 - main - INFO - 正在关闭系统...
2025-06-10 18:15:06,191 - main - INFO - 正在关闭系统...
2025-06-10 18:15:06,193 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 18:15:06,194 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 18:15:06,193 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 18:15:06,195 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 18:15:06,195 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 18:15:06,195 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 18:15:06,196 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 18:15:06,197 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 18:15:06,198 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 18:15:06,195 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 18:15:06,199 - main - INFO - 数据库连接已关闭
2025-06-10 18:15:06,199 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 18:15:06,199 - main - INFO - 系统已关闭
2025-06-10 18:15:06,200 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 18:15:06,203 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 18:15:06,203 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 18:15:06,203 - main - INFO - 数据库连接已关闭
2025-06-10 18:15:06,203 - main - INFO - 系统已关闭
2025-06-10 18:15:17,975 - main - INFO - 静态文件服务已配置
2025-06-10 18:15:17,977 - main - INFO - 模板引擎已配置
2025-06-10 18:15:17,988 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 18:15:17,989 - main - INFO - 初始化系统配置...
2025-06-10 18:15:17,999 - main - INFO - 初始化数据库连接...
2025-06-10 18:15:17,999 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 18:15:18,097 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 18:15:18,154 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 18:15:18,159 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:15:18,163 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 18:15:18,164 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:15:18,166 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 18:15:18,166 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:15:18,168 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:15:18,171 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 18:15:18,172 - sqlalchemy.engine.Engine - INFO - [generated in 0.00091s] ()
2025-06-10 18:15:18,173 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 18:15:18,178 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 18:15:18,181 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:15:18,182 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 18:15:18,182 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:15:18,186 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 18:15:18,187 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:15:18,190 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 18:15:18,199 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 18:15:18,199 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 18:15:18,200 - main - INFO - 检查AI服务状态...
2025-06-10 18:15:18,200 - main - INFO - AI服务就绪
2025-06-10 18:15:18,201 - main - INFO - 系统启动完成
2025-06-10 18:15:18,201 - main - INFO - 系统信息:
2025-06-10 18:15:18,201 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 18:15:18,201 - main - INFO -   - 版本: 2.0.0
2025-06-10 18:15:18,201 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 18:15:18,203 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 18:15:18,204 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 18:15:18,204 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 18:17:16,524 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:17:17,309 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 18:17:18,189 - sqlalchemy.engine.Engine - INFO - [cached since 120s ago] ()
2025-06-10 18:17:18,191 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 18:17:18,191 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 18:18:22,935 - main - INFO - 正在关闭系统...
2025-06-10 18:18:23,003 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 18:18:23,029 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 18:18:23,119 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 18:18:23,273 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 18:18:23,370 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 18:18:23,385 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 18:18:23,398 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 18:18:23,498 - main - INFO - 数据库连接已关闭
2025-06-10 18:18:23,622 - main - INFO - 系统已关闭
2025-06-10 18:18:30,684 - main - INFO - 静态文件服务已配置
2025-06-10 18:18:30,685 - main - INFO - 模板引擎已配置
2025-06-10 18:18:30,749 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 18:18:30,752 - main - INFO - 初始化系统配置...
2025-06-10 18:18:30,762 - main - INFO - 初始化数据库连接...
2025-06-10 18:18:30,762 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 18:18:30,860 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 18:18:31,298 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 18:18:31,467 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:18:31,556 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 18:18:31,578 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:18:31,665 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 18:18:31,669 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:18:31,681 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:18:31,682 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 18:18:31,683 - sqlalchemy.engine.Engine - INFO - [generated in 0.00078s] ()
2025-06-10 18:18:31,684 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 18:18:31,685 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 18:18:31,686 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:18:31,687 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 18:18:31,687 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:18:31,692 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 18:18:31,703 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:18:31,714 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 18:18:31,720 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 18:18:31,728 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 18:18:31,729 - main - INFO - 检查AI服务状态...
2025-06-10 18:18:31,729 - main - INFO - AI服务就绪
2025-06-10 18:18:31,730 - main - INFO - 系统启动完成
2025-06-10 18:18:31,730 - main - INFO - 系统信息:
2025-06-10 18:18:31,730 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 18:18:31,731 - main - INFO -   - 版本: 2.0.0
2025-06-10 18:18:31,731 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 18:18:31,731 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 18:18:31,731 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 18:18:31,732 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 18:19:14,437 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:19:14,439 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 18:19:14,470 - sqlalchemy.engine.Engine - INFO - [cached since 42.79s ago] ()
2025-06-10 18:19:14,702 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 18:19:14,703 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 18:19:25,952 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:19:25,953 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 18:19:25,953 - sqlalchemy.engine.Engine - INFO - [cached since 54.27s ago] ()
2025-06-10 18:19:25,954 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 18:19:25,954 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 18:19:40,946 - main - INFO - 正在关闭系统...
2025-06-10 18:19:40,948 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 18:19:40,948 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 18:19:40,949 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 18:19:40,949 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 18:19:40,950 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 18:19:40,950 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 18:19:40,950 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 18:19:40,954 - main - INFO - 数据库连接已关闭
2025-06-10 18:19:40,954 - main - INFO - 系统已关闭
2025-06-10 18:22:25,987 - main - INFO - 静态文件服务已配置
2025-06-10 18:22:25,987 - main - INFO - 模板引擎已配置
2025-06-10 18:22:25,994 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 18:22:25,995 - main - INFO - 初始化系统配置...
2025-06-10 18:22:26,003 - main - INFO - 初始化数据库连接...
2025-06-10 18:22:26,004 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 18:22:26,081 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 18:22:26,101 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 18:22:26,105 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:22:26,109 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 18:22:26,111 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:22:26,112 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 18:22:26,113 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:22:26,115 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:22:26,116 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 18:22:26,120 - sqlalchemy.engine.Engine - INFO - [generated in 0.00431s] ()
2025-06-10 18:22:26,121 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 18:22:26,125 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 18:22:26,143 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:22:26,231 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 18:22:26,232 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:22:26,235 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 18:22:26,236 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:22:26,241 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 18:22:26,245 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 18:22:26,246 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 18:22:26,246 - main - INFO - 检查AI服务状态...
2025-06-10 18:22:26,246 - main - INFO - AI服务就绪
2025-06-10 18:22:26,246 - main - INFO - 系统启动完成
2025-06-10 18:22:26,247 - main - INFO - 系统信息:
2025-06-10 18:22:26,247 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 18:22:26,247 - main - INFO -   - 版本: 2.0.0
2025-06-10 18:22:26,247 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 18:22:26,247 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 18:22:26,248 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 18:22:26,248 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 18:26:49,917 - main - INFO - 正在关闭系统...
2025-06-10 18:26:49,917 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 18:26:49,918 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 18:26:49,918 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 18:26:49,919 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 18:26:49,919 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 18:26:49,919 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 18:26:49,919 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 18:26:49,920 - main - INFO - 数据库连接已关闭
2025-06-10 18:26:49,920 - main - INFO - 系统已关闭
2025-06-10 18:27:02,357 - main - INFO - 静态文件服务已配置
2025-06-10 18:27:02,366 - main - INFO - 模板引擎已配置
2025-06-10 18:27:02,373 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 18:27:02,374 - main - INFO - 初始化系统配置...
2025-06-10 18:27:02,383 - main - INFO - 初始化数据库连接...
2025-06-10 18:27:02,383 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 18:27:02,461 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 18:27:02,503 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 18:27:02,510 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:27:02,512 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 18:27:02,512 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:27:02,514 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 18:27:02,514 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:27:02,516 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:27:02,519 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 18:27:02,520 - sqlalchemy.engine.Engine - INFO - [generated in 0.00061s] ()
2025-06-10 18:27:02,524 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 18:27:02,529 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 18:27:02,530 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:27:02,531 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 18:27:02,532 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:27:02,534 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 18:27:02,536 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:27:02,540 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 18:27:02,546 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 18:27:02,547 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 18:27:02,547 - main - INFO - 检查AI服务状态...
2025-06-10 18:27:02,547 - main - INFO - AI服务就绪
2025-06-10 18:27:02,547 - main - INFO - 系统启动完成
2025-06-10 18:27:02,548 - main - INFO - 系统信息:
2025-06-10 18:27:02,549 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 18:27:02,549 - main - INFO -   - 版本: 2.0.0
2025-06-10 18:27:02,549 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 18:27:02,549 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 18:27:02,549 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 18:27:02,550 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 18:34:09,743 - main - INFO - 正在关闭系统...
2025-06-10 18:34:09,744 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 18:34:09,745 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 18:34:09,745 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 18:34:09,745 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 18:34:09,746 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 18:34:09,746 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 18:34:09,747 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 18:34:09,822 - main - INFO - 数据库连接已关闭
2025-06-10 18:34:09,822 - main - INFO - 系统已关闭
2025-06-10 18:34:20,289 - main - INFO - 静态文件服务已配置
2025-06-10 18:34:20,292 - main - INFO - 模板引擎已配置
2025-06-10 18:34:20,314 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 18:34:20,314 - main - INFO - 初始化系统配置...
2025-06-10 18:34:20,318 - main - INFO - 初始化数据库连接...
2025-06-10 18:34:20,319 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 18:34:20,441 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 18:34:20,469 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 18:34:20,473 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:34:20,476 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 18:34:20,477 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:34:20,481 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 18:34:20,481 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:34:20,488 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:34:20,489 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 18:34:20,490 - sqlalchemy.engine.Engine - INFO - [generated in 0.00059s] ()
2025-06-10 18:34:20,492 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 18:34:20,495 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 18:34:20,498 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:34:20,499 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 18:34:20,499 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:34:20,502 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 18:34:20,503 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 18:34:20,506 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 18:34:20,507 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 18:34:20,508 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 18:34:20,514 - main - INFO - 检查AI服务状态...
2025-06-10 18:34:20,514 - main - INFO - AI服务就绪
2025-06-10 18:34:20,515 - main - INFO - 系统启动完成
2025-06-10 18:34:20,515 - main - INFO - 系统信息:
2025-06-10 18:34:20,515 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 18:34:20,515 - main - INFO -   - 版本: 2.0.0
2025-06-10 18:34:20,515 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 18:34:20,515 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 18:34:20,515 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 18:34:20,516 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 18:35:24,852 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:35:24,859 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 18:35:24,859 - sqlalchemy.engine.Engine - INFO - [cached since 64.37s ago] ()
2025-06-10 18:35:24,860 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=1
2025-06-10 18:35:24,862 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 18:35:24,870 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 18:35:24,878 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:35:24,890 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 18:35:24,893 - sqlalchemy.engine.Engine - INFO - [generated in 0.00269s] ()
2025-06-10 18:35:24,899 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 18:35:24,932 - sqlalchemy.engine.Engine - INFO - [generated in 0.03352s] (0, 1)
2025-06-10 18:35:24,943 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s)
2025-06-10 18:35:24,951 - sqlalchemy.engine.Engine - INFO - [generated in 0.00776s] (4,)
2025-06-10 18:35:24,958 - app.services.requirement_service - INFO -  获取需求列表成功: 返回1条记录，总数=4
2025-06-10 18:35:24,962 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 18:35:24,986 - app.services.requirement_service - INFO -  获取分析结果列表: 页码=1, 大小=1
2025-06-10 18:35:24,989 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:35:24,991 - sqlalchemy.engine.Engine - INFO - SELECT count(ai_analysis_results.id) AS count_1 
FROM ai_analysis_results
2025-06-10 18:35:24,992 - sqlalchemy.engine.Engine - INFO - [generated in 0.00138s] ()
2025-06-10 18:35:24,996 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.id, ai_analysis_results.req_id, ai_analysis_results.analysis_result, ai_analysis_results.structured_result, ai_analysis_results.key_points, ai_analysis_results.risk_assessment, ai_analysis_results.suggestions, ai_analysis_results.manual_adjust, ai_analysis_results.is_manually_adjusted, ai_analysis_results.adjusted_by, ai_analysis_results.adjusted_time, ai_analysis_results.ai_model, ai_analysis_results.ai_version, ai_analysis_results.analysis_config, ai_analysis_results.analysis_status, ai_analysis_results.error_message, ai_analysis_results.processing_time, ai_analysis_results.created_time, ai_analysis_results.update_time 
FROM ai_analysis_results ORDER BY ai_analysis_results.created_time DESC 
 LIMIT %s, %s
2025-06-10 18:35:25,006 - sqlalchemy.engine.Engine - INFO - [generated in 0.01014s] (0, 1)
2025-06-10 18:35:25,007 - app.services.requirement_service - INFO -  获取分析结果列表成功: 返回0条记录，总数=0
2025-06-10 18:35:25,010 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 18:35:38,409 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 18:35:38,409 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:35:38,410 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 18:35:38,484 - sqlalchemy.engine.Engine - INFO - [cached since 13.59s ago] ()
2025-06-10 18:35:38,520 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 18:35:38,555 - sqlalchemy.engine.Engine - INFO - [cached since 13.66s ago] (0, 20)
2025-06-10 18:35:38,589 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 18:35:38,602 - sqlalchemy.engine.Engine - INFO - [cached since 13.66s ago] (1, 2, 3, 4)
2025-06-10 18:35:38,605 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 18:35:38,606 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 18:35:45,356 - app.services.requirement_service - INFO -  获取分析结果列表: 页码=1, 大小=20
2025-06-10 18:35:45,357 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:35:45,358 - sqlalchemy.engine.Engine - INFO - SELECT count(ai_analysis_results.id) AS count_1 
FROM ai_analysis_results
2025-06-10 18:35:45,358 - sqlalchemy.engine.Engine - INFO - [cached since 20.37s ago] ()
2025-06-10 18:35:45,390 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.id, ai_analysis_results.req_id, ai_analysis_results.analysis_result, ai_analysis_results.structured_result, ai_analysis_results.key_points, ai_analysis_results.risk_assessment, ai_analysis_results.suggestions, ai_analysis_results.manual_adjust, ai_analysis_results.is_manually_adjusted, ai_analysis_results.adjusted_by, ai_analysis_results.adjusted_time, ai_analysis_results.ai_model, ai_analysis_results.ai_version, ai_analysis_results.analysis_config, ai_analysis_results.analysis_status, ai_analysis_results.error_message, ai_analysis_results.processing_time, ai_analysis_results.created_time, ai_analysis_results.update_time 
FROM ai_analysis_results ORDER BY ai_analysis_results.created_time DESC 
 LIMIT %s, %s
2025-06-10 18:35:45,394 - sqlalchemy.engine.Engine - INFO - [cached since 20.4s ago] (0, 20)
2025-06-10 18:35:45,396 - app.services.requirement_service - INFO -  获取分析结果列表成功: 返回0条记录，总数=0
2025-06-10 18:35:45,396 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 18:35:57,030 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 18:35:57,045 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:35:57,046 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 18:35:57,046 - sqlalchemy.engine.Engine - INFO - [cached since 32.16s ago] ()
2025-06-10 18:35:57,057 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 18:35:57,059 - sqlalchemy.engine.Engine - INFO - [cached since 32.16s ago] (0, 20)
2025-06-10 18:35:57,061 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 18:35:57,061 - sqlalchemy.engine.Engine - INFO - [cached since 32.12s ago] (1, 2, 3, 4)
2025-06-10 18:35:57,063 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 18:35:57,087 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 18:36:06,136 - app.services.requirement_service - INFO -  获取分析结果列表: 页码=1, 大小=20
2025-06-10 18:36:06,137 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:36:06,137 - sqlalchemy.engine.Engine - INFO - SELECT count(ai_analysis_results.id) AS count_1 
FROM ai_analysis_results
2025-06-10 18:36:06,140 - sqlalchemy.engine.Engine - INFO - [cached since 41.15s ago] ()
2025-06-10 18:36:06,144 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.id, ai_analysis_results.req_id, ai_analysis_results.analysis_result, ai_analysis_results.structured_result, ai_analysis_results.key_points, ai_analysis_results.risk_assessment, ai_analysis_results.suggestions, ai_analysis_results.manual_adjust, ai_analysis_results.is_manually_adjusted, ai_analysis_results.adjusted_by, ai_analysis_results.adjusted_time, ai_analysis_results.ai_model, ai_analysis_results.ai_version, ai_analysis_results.analysis_config, ai_analysis_results.analysis_status, ai_analysis_results.error_message, ai_analysis_results.processing_time, ai_analysis_results.created_time, ai_analysis_results.update_time 
FROM ai_analysis_results ORDER BY ai_analysis_results.created_time DESC 
 LIMIT %s, %s
2025-06-10 18:36:06,164 - sqlalchemy.engine.Engine - INFO - [cached since 41.17s ago] (0, 20)
2025-06-10 18:36:06,177 - app.services.requirement_service - INFO -  获取分析结果列表成功: 返回0条记录，总数=0
2025-06-10 18:36:06,179 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 18:36:06,789 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 18:36:06,790 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:36:06,791 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 18:36:06,791 - sqlalchemy.engine.Engine - INFO - [cached since 41.9s ago] ()
2025-06-10 18:36:06,798 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 18:36:06,844 - sqlalchemy.engine.Engine - INFO - [cached since 41.94s ago] (0, 20)
2025-06-10 18:36:06,847 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 18:36:06,910 - sqlalchemy.engine.Engine - INFO - [cached since 41.97s ago] (1, 2, 3, 4)
2025-06-10 18:36:06,912 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 18:36:06,913 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 18:36:28,849 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=1
2025-06-10 18:36:28,979 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:36:29,146 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 18:36:29,211 - sqlalchemy.engine.Engine - INFO - [cached since 64.32s ago] ()
2025-06-10 18:36:29,413 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 18:36:30,193 - sqlalchemy.engine.Engine - INFO - [cached since 65.29s ago] (0, 1)
2025-06-10 18:36:30,752 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s)
2025-06-10 18:36:30,961 - sqlalchemy.engine.Engine - INFO - [cached since 66.02s ago] (4,)
2025-06-10 18:36:31,275 - app.services.requirement_service - INFO -  获取需求列表成功: 返回1条记录，总数=4
2025-06-10 18:36:31,340 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 18:36:31,493 - app.services.requirement_service - INFO -  获取分析结果列表: 页码=1, 大小=1
2025-06-10 18:36:31,564 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:36:31,567 - sqlalchemy.engine.Engine - INFO - SELECT count(ai_analysis_results.id) AS count_1 
FROM ai_analysis_results
2025-06-10 18:36:31,572 - sqlalchemy.engine.Engine - INFO - [cached since 66.58s ago] ()
2025-06-10 18:36:31,597 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.id, ai_analysis_results.req_id, ai_analysis_results.analysis_result, ai_analysis_results.structured_result, ai_analysis_results.key_points, ai_analysis_results.risk_assessment, ai_analysis_results.suggestions, ai_analysis_results.manual_adjust, ai_analysis_results.is_manually_adjusted, ai_analysis_results.adjusted_by, ai_analysis_results.adjusted_time, ai_analysis_results.ai_model, ai_analysis_results.ai_version, ai_analysis_results.analysis_config, ai_analysis_results.analysis_status, ai_analysis_results.error_message, ai_analysis_results.processing_time, ai_analysis_results.created_time, ai_analysis_results.update_time 
FROM ai_analysis_results ORDER BY ai_analysis_results.created_time DESC 
 LIMIT %s, %s
2025-06-10 18:36:31,614 - sqlalchemy.engine.Engine - INFO - [cached since 66.62s ago] (0, 1)
2025-06-10 18:36:31,624 - app.services.requirement_service - INFO -  获取分析结果列表成功: 返回0条记录，总数=0
2025-06-10 18:36:31,627 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 18:36:45,696 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 18:36:45,697 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:36:45,698 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 18:36:45,699 - sqlalchemy.engine.Engine - INFO - [cached since 80.81s ago] ()
2025-06-10 18:36:45,709 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 18:36:45,828 - sqlalchemy.engine.Engine - INFO - [cached since 80.93s ago] (0, 20)
2025-06-10 18:36:45,833 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 18:36:45,844 - sqlalchemy.engine.Engine - INFO - [cached since 80.9s ago] (1, 2, 3, 4)
2025-06-10 18:36:45,847 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 18:36:45,847 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 18:36:54,478 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 18:36:54,535 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:36:54,541 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 18:36:54,549 - sqlalchemy.engine.Engine - INFO - [cached since 89.66s ago] ()
2025-06-10 18:36:54,577 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 18:36:54,663 - sqlalchemy.engine.Engine - INFO - [cached since 89.76s ago] (0, 20)
2025-06-10 18:36:54,680 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 18:36:54,695 - sqlalchemy.engine.Engine - INFO - [cached since 89.75s ago] (1, 2, 3, 4)
2025-06-10 18:36:54,712 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 18:36:54,713 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 18:37:01,369 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 18:37:01,370 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:37:01,509 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 18:37:01,511 - sqlalchemy.engine.Engine - INFO - [cached since 96.62s ago] ()
2025-06-10 18:37:01,515 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 18:37:01,518 - sqlalchemy.engine.Engine - INFO - [cached since 96.62s ago] (0, 20)
2025-06-10 18:37:01,643 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 18:37:01,826 - sqlalchemy.engine.Engine - INFO - [cached since 96.88s ago] (1, 2, 3, 4)
2025-06-10 18:37:01,849 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 18:37:01,852 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 18:37:14,550 - app.services.requirement_service - INFO -  获取分析结果列表: 页码=1, 大小=20
2025-06-10 18:37:14,552 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:37:14,552 - sqlalchemy.engine.Engine - INFO - SELECT count(ai_analysis_results.id) AS count_1 
FROM ai_analysis_results
2025-06-10 18:37:14,553 - sqlalchemy.engine.Engine - INFO - [cached since 109.6s ago] ()
2025-06-10 18:37:14,554 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.id, ai_analysis_results.req_id, ai_analysis_results.analysis_result, ai_analysis_results.structured_result, ai_analysis_results.key_points, ai_analysis_results.risk_assessment, ai_analysis_results.suggestions, ai_analysis_results.manual_adjust, ai_analysis_results.is_manually_adjusted, ai_analysis_results.adjusted_by, ai_analysis_results.adjusted_time, ai_analysis_results.ai_model, ai_analysis_results.ai_version, ai_analysis_results.analysis_config, ai_analysis_results.analysis_status, ai_analysis_results.error_message, ai_analysis_results.processing_time, ai_analysis_results.created_time, ai_analysis_results.update_time 
FROM ai_analysis_results ORDER BY ai_analysis_results.created_time DESC 
 LIMIT %s, %s
2025-06-10 18:37:14,566 - sqlalchemy.engine.Engine - INFO - [cached since 109.6s ago] (0, 20)
2025-06-10 18:37:14,569 - app.services.requirement_service - INFO -  获取分析结果列表成功: 返回0条记录，总数=0
2025-06-10 18:37:14,569 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 18:37:16,644 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 18:37:16,675 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 18:37:16,679 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 18:37:16,681 - sqlalchemy.engine.Engine - INFO - [cached since 111.8s ago] ()
2025-06-10 18:37:16,684 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 18:37:16,685 - sqlalchemy.engine.Engine - INFO - [cached since 111.8s ago] (0, 20)
2025-06-10 18:37:16,697 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 18:37:16,699 - sqlalchemy.engine.Engine - INFO - [cached since 111.8s ago] (1, 2, 3, 4)
2025-06-10 18:37:16,701 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 18:37:16,701 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:19:39,127 - app.services.requirement_service - INFO -  获取分析结果列表: 页码=1, 大小=20
2025-06-10 19:19:39,129 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:19:39,131 - sqlalchemy.engine.Engine - INFO - SELECT count(ai_analysis_results.id) AS count_1 
FROM ai_analysis_results
2025-06-10 19:19:39,134 - sqlalchemy.engine.Engine - INFO - [cached since 2654s ago] ()
2025-06-10 19:19:39,139 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.id, ai_analysis_results.req_id, ai_analysis_results.analysis_result, ai_analysis_results.structured_result, ai_analysis_results.key_points, ai_analysis_results.risk_assessment, ai_analysis_results.suggestions, ai_analysis_results.manual_adjust, ai_analysis_results.is_manually_adjusted, ai_analysis_results.adjusted_by, ai_analysis_results.adjusted_time, ai_analysis_results.ai_model, ai_analysis_results.ai_version, ai_analysis_results.analysis_config, ai_analysis_results.analysis_status, ai_analysis_results.error_message, ai_analysis_results.processing_time, ai_analysis_results.created_time, ai_analysis_results.update_time 
FROM ai_analysis_results ORDER BY ai_analysis_results.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:19:39,140 - sqlalchemy.engine.Engine - INFO - [cached since 2654s ago] (0, 20)
2025-06-10 19:19:39,143 - app.services.requirement_service - INFO -  获取分析结果列表成功: 返回0条记录，总数=0
2025-06-10 19:19:39,144 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:19:44,137 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:19:44,139 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:19:44,156 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:19:44,157 - sqlalchemy.engine.Engine - INFO - [cached since 2659s ago] ()
2025-06-10 19:19:44,162 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:19:44,163 - sqlalchemy.engine.Engine - INFO - [cached since 2659s ago] (0, 20)
2025-06-10 19:19:44,166 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:19:44,166 - sqlalchemy.engine.Engine - INFO - [cached since 2659s ago] (1, 2, 3, 4)
2025-06-10 19:19:44,168 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:19:44,168 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:19:54,030 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:19:54,049 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:19:54,050 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:19:54,052 - sqlalchemy.engine.Engine - INFO - [cached since 2669s ago] ()
2025-06-10 19:19:54,054 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:19:54,055 - sqlalchemy.engine.Engine - INFO - [cached since 2669s ago] (0, 20)
2025-06-10 19:19:54,065 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:19:54,066 - sqlalchemy.engine.Engine - INFO - [cached since 2669s ago] (1, 2, 3, 4)
2025-06-10 19:19:54,068 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:19:54,068 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:19:58,148 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:19:58,149 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:19:58,151 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:19:58,152 - sqlalchemy.engine.Engine - INFO - [cached since 2673s ago] ()
2025-06-10 19:19:58,154 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:19:58,155 - sqlalchemy.engine.Engine - INFO - [cached since 2673s ago] (0, 20)
2025-06-10 19:19:58,157 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:19:58,164 - sqlalchemy.engine.Engine - INFO - [cached since 2673s ago] (1, 2, 3, 4)
2025-06-10 19:19:58,166 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:19:58,169 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:19:58,184 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:19:58,185 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:19:58,186 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:19:58,187 - sqlalchemy.engine.Engine - INFO - [cached since 2673s ago] ()
2025-06-10 19:19:58,189 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:19:58,190 - sqlalchemy.engine.Engine - INFO - [cached since 2673s ago] (0, 20)
2025-06-10 19:19:58,192 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:19:58,199 - sqlalchemy.engine.Engine - INFO - [cached since 2673s ago] (1, 2, 3, 4)
2025-06-10 19:19:58,201 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:19:58,202 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:19:58,215 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:19:58,216 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:19:58,219 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:19:58,223 - sqlalchemy.engine.Engine - INFO - [cached since 2673s ago] ()
2025-06-10 19:19:58,236 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:19:58,249 - sqlalchemy.engine.Engine - INFO - [cached since 2673s ago] (0, 20)
2025-06-10 19:19:58,252 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:19:58,256 - sqlalchemy.engine.Engine - INFO - [cached since 2673s ago] (1, 2, 3, 4)
2025-06-10 19:19:58,263 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:19:58,265 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:19:58,273 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:19:58,274 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:19:58,274 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:19:58,285 - sqlalchemy.engine.Engine - INFO - [cached since 2673s ago] ()
2025-06-10 19:19:58,286 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:19:58,286 - sqlalchemy.engine.Engine - INFO - [cached since 2673s ago] (0, 20)
2025-06-10 19:19:58,288 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:19:58,288 - sqlalchemy.engine.Engine - INFO - [cached since 2673s ago] (1, 2, 3, 4)
2025-06-10 19:19:58,289 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:19:58,348 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:19:58,356 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:19:58,358 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:19:58,366 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:19:58,366 - sqlalchemy.engine.Engine - INFO - [cached since 2673s ago] ()
2025-06-10 19:19:58,368 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:19:58,369 - sqlalchemy.engine.Engine - INFO - [cached since 2673s ago] (0, 20)
2025-06-10 19:19:58,371 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:19:58,373 - sqlalchemy.engine.Engine - INFO - [cached since 2673s ago] (1, 2, 3, 4)
2025-06-10 19:19:58,374 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:19:58,381 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:19:58,400 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:19:58,403 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:19:58,404 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:19:58,405 - sqlalchemy.engine.Engine - INFO - [cached since 2674s ago] ()
2025-06-10 19:19:58,406 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:19:58,407 - sqlalchemy.engine.Engine - INFO - [cached since 2674s ago] (0, 20)
2025-06-10 19:19:58,410 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:19:58,417 - sqlalchemy.engine.Engine - INFO - [cached since 2673s ago] (1, 2, 3, 4)
2025-06-10 19:19:58,419 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:19:58,419 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:19:58,433 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:19:58,439 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:19:58,440 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:19:58,441 - sqlalchemy.engine.Engine - INFO - [cached since 2674s ago] ()
2025-06-10 19:19:58,442 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:19:58,450 - sqlalchemy.engine.Engine - INFO - [cached since 2674s ago] (0, 20)
2025-06-10 19:19:58,456 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:19:58,464 - sqlalchemy.engine.Engine - INFO - [cached since 2674s ago] (1, 2, 3, 4)
2025-06-10 19:19:58,465 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:19:58,466 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:19:58,473 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:19:58,474 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:19:58,475 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:19:58,487 - sqlalchemy.engine.Engine - INFO - [cached since 2674s ago] ()
2025-06-10 19:19:58,489 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:19:58,490 - sqlalchemy.engine.Engine - INFO - [cached since 2674s ago] (0, 20)
2025-06-10 19:19:58,491 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:19:58,495 - sqlalchemy.engine.Engine - INFO - [cached since 2674s ago] (1, 2, 3, 4)
2025-06-10 19:19:58,497 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:19:58,500 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:19:58,515 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:19:58,516 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:19:58,517 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:19:58,518 - sqlalchemy.engine.Engine - INFO - [cached since 2674s ago] ()
2025-06-10 19:19:58,520 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:19:58,522 - sqlalchemy.engine.Engine - INFO - [cached since 2674s ago] (0, 20)
2025-06-10 19:19:58,524 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:19:58,526 - sqlalchemy.engine.Engine - INFO - [cached since 2674s ago] (1, 2, 3, 4)
2025-06-10 19:19:58,528 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:19:58,529 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:06,751 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:06,753 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:06,753 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:06,754 - sqlalchemy.engine.Engine - INFO - [cached since 2682s ago] ()
2025-06-10 19:20:06,756 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:06,756 - sqlalchemy.engine.Engine - INFO - [cached since 2682s ago] (0, 20)
2025-06-10 19:20:06,759 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:06,767 - sqlalchemy.engine.Engine - INFO - [cached since 2682s ago] (1, 2, 3, 4)
2025-06-10 19:20:06,773 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:06,774 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:14,633 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:14,635 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:14,637 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:14,637 - sqlalchemy.engine.Engine - INFO - [generated in 0.00089s] ('MEDIUM',)
2025-06-10 19:20:14,639 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:14,641 - sqlalchemy.engine.Engine - INFO - [generated in 0.00155s] ('MEDIUM', 0, 20)
2025-06-10 19:20:14,644 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:14,649 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:14,651 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:14,653 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:14,659 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:14,660 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:14,671 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:14,672 - sqlalchemy.engine.Engine - INFO - [cached since 0.03553s ago] ('MEDIUM',)
2025-06-10 19:20:14,676 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:14,680 - sqlalchemy.engine.Engine - INFO - [cached since 0.04107s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:14,683 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:14,685 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:14,687 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:14,687 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:14,697 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:14,704 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:14,704 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:14,707 - sqlalchemy.engine.Engine - INFO - [cached since 0.07012s ago] ('MEDIUM',)
2025-06-10 19:20:14,708 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:14,711 - sqlalchemy.engine.Engine - INFO - [cached since 0.07136s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:14,713 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:14,718 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:14,720 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:14,720 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:14,725 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:14,727 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:14,728 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:14,728 - sqlalchemy.engine.Engine - INFO - [cached since 0.09195s ago] ('MEDIUM',)
2025-06-10 19:20:14,730 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:14,731 - sqlalchemy.engine.Engine - INFO - [cached since 0.09189s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:14,737 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:14,741 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:14,743 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:14,745 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:14,750 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:14,752 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:14,753 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:14,754 - sqlalchemy.engine.Engine - INFO - [cached since 0.1179s ago] ('MEDIUM',)
2025-06-10 19:20:14,756 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:14,757 - sqlalchemy.engine.Engine - INFO - [cached since 0.1175s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:14,760 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:14,770 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:14,774 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:14,774 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:14,786 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:14,787 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:14,788 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:14,789 - sqlalchemy.engine.Engine - INFO - [cached since 0.1523s ago] ('MEDIUM',)
2025-06-10 19:20:14,790 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:14,791 - sqlalchemy.engine.Engine - INFO - [cached since 0.152s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:14,794 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:14,801 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:14,804 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:14,805 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:14,810 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:14,818 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:14,820 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:14,821 - sqlalchemy.engine.Engine - INFO - [cached since 0.1847s ago] ('MEDIUM',)
2025-06-10 19:20:14,824 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:14,825 - sqlalchemy.engine.Engine - INFO - [cached since 0.1856s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:14,828 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:14,831 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:14,833 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:14,833 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:14,839 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:14,840 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:14,841 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:14,842 - sqlalchemy.engine.Engine - INFO - [cached since 0.2058s ago] ('MEDIUM',)
2025-06-10 19:20:14,845 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:14,849 - sqlalchemy.engine.Engine - INFO - [cached since 0.2098s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:14,853 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:14,856 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:14,858 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:14,858 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:14,863 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:14,867 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:14,868 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:14,869 - sqlalchemy.engine.Engine - INFO - [cached since 0.2321s ago] ('MEDIUM',)
2025-06-10 19:20:14,870 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:14,871 - sqlalchemy.engine.Engine - INFO - [cached since 0.2318s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:14,873 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:14,876 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:14,877 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:14,878 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:14,891 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:14,893 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:14,897 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:14,900 - sqlalchemy.engine.Engine - INFO - [cached since 0.2637s ago] ('MEDIUM',)
2025-06-10 19:20:14,902 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:14,905 - sqlalchemy.engine.Engine - INFO - [cached since 0.2657s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:14,908 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:14,910 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:14,912 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:14,913 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:14,919 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:14,921 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:14,922 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:14,923 - sqlalchemy.engine.Engine - INFO - [cached since 0.2861s ago] ('MEDIUM',)
2025-06-10 19:20:14,924 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:14,925 - sqlalchemy.engine.Engine - INFO - [cached since 0.2857s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:14,927 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:14,935 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:14,937 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:14,939 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:14,945 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:14,948 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:14,950 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:14,951 - sqlalchemy.engine.Engine - INFO - [cached since 0.3143s ago] ('MEDIUM',)
2025-06-10 19:20:14,952 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:14,953 - sqlalchemy.engine.Engine - INFO - [cached since 0.3138s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:14,956 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:14,958 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:14,959 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:14,960 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:14,975 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:14,979 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:14,985 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:14,987 - sqlalchemy.engine.Engine - INFO - [cached since 0.3509s ago] ('MEDIUM',)
2025-06-10 19:20:14,989 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:14,990 - sqlalchemy.engine.Engine - INFO - [cached since 0.3507s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:14,992 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:14,998 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:15,000 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:15,000 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:15,006 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:15,007 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:15,008 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:15,008 - sqlalchemy.engine.Engine - INFO - [cached since 0.3717s ago] ('MEDIUM',)
2025-06-10 19:20:15,010 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:15,017 - sqlalchemy.engine.Engine - INFO - [cached since 0.3772s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:15,020 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:15,025 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:15,028 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:15,032 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:15,040 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:15,041 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:15,042 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:15,042 - sqlalchemy.engine.Engine - INFO - [cached since 0.406s ago] ('MEDIUM',)
2025-06-10 19:20:15,044 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:15,052 - sqlalchemy.engine.Engine - INFO - [cached since 0.413s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:15,056 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:15,059 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:15,061 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:15,064 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:15,071 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:15,073 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:15,074 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:15,074 - sqlalchemy.engine.Engine - INFO - [cached since 0.4377s ago] ('MEDIUM',)
2025-06-10 19:20:15,076 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:15,076 - sqlalchemy.engine.Engine - INFO - [cached since 0.4372s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:15,083 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:15,084 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:15,086 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:15,087 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:15,092 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:15,099 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:15,101 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:15,102 - sqlalchemy.engine.Engine - INFO - [cached since 0.4656s ago] ('MEDIUM',)
2025-06-10 19:20:15,104 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:15,105 - sqlalchemy.engine.Engine - INFO - [cached since 0.4662s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:15,107 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:15,109 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:15,111 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:15,118 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:15,125 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:15,129 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:15,133 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:15,134 - sqlalchemy.engine.Engine - INFO - [cached since 0.4976s ago] ('MEDIUM',)
2025-06-10 19:20:15,136 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:15,139 - sqlalchemy.engine.Engine - INFO - [cached since 0.4993s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:15,141 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:15,144 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:15,153 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:15,154 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:15,160 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:15,163 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:15,167 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.priority = %s
2025-06-10 19:20:15,168 - sqlalchemy.engine.Engine - INFO - [cached since 0.5316s ago] ('MEDIUM',)
2025-06-10 19:20:15,170 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.priority = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:15,172 - sqlalchemy.engine.Engine - INFO - [cached since 0.5326s ago] ('MEDIUM', 0, 20)
2025-06-10 19:20:15,174 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s)
2025-06-10 19:20:15,183 - sqlalchemy.engine.Engine - INFO - [cached since 2690s ago] (4, 2)
2025-06-10 19:20:15,185 - app.services.requirement_service - INFO -  获取需求列表成功: 返回2条记录，总数=2
2025-06-10 19:20:15,186 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:20,704 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:20,706 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:20,707 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:20,707 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:20,709 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:20,709 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:20,712 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:20,714 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:20,716 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:20,716 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:20,721 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:20,723 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:20,724 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:20,730 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:20,732 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:20,733 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:20,735 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:20,737 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:20,739 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:20,740 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:20,748 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:20,749 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:20,749 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:20,750 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:20,751 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:20,752 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:20,754 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:20,755 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:20,757 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:20,757 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:20,762 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:20,765 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:20,767 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:20,767 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:20,769 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:20,771 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:20,773 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:20,776 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:20,778 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:20,778 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:20,783 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:20,788 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:20,789 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:20,789 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:20,791 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:20,792 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:20,795 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:20,799 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:20,800 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:20,801 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:20,805 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:20,807 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:20,809 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:20,811 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:20,812 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:20,814 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:20,816 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:20,819 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:20,820 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:20,821 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:20,828 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:20,832 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:20,832 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:20,833 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:20,835 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:20,836 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:20,838 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:20,840 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:20,842 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:20,842 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:20,846 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:20,851 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:20,852 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:20,853 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:20,854 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:20,855 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:20,858 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:20,860 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:20,862 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:20,863 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:20,872 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:20,874 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:20,875 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:20,875 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:20,877 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:20,878 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:20,883 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:20,885 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:20,887 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:20,887 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:20,892 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:20,894 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:20,896 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:20,900 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:20,902 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:20,902 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:20,905 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:20,907 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:20,908 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:20,909 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:20,914 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:20,919 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:20,920 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:20,920 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:20,922 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:20,923 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:20,925 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:20,929 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:20,931 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:20,931 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:20,937 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:20,938 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:20,939 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:20,939 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:20,941 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:20,941 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:20,943 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:20,949 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:20,951 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:20,952 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:20,958 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:20,959 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:20,959 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:20,960 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:20,962 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:20,966 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:20,969 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:20,970 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:20,972 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:20,973 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:20,978 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:20,980 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:20,985 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:20,986 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:20,988 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:20,989 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:20,991 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:20,993 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:20,996 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:20,996 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:21,002 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:21,004 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:21,005 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:21,008 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:21,010 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:21,011 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:21,013 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:21,018 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:21,019 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:21,022 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:21,042 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:21,044 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:21,052 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:21,053 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:21,057 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:21,058 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:21,060 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:21,062 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:21,063 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:21,065 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:21,070 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:21,072 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:21,074 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:21,075 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:21,077 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:21,084 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:21,086 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:21,088 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:21,090 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:21,092 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:21,102 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:21,104 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:21,105 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:21,105 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:21,107 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:21,107 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:21,110 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:21,117 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:21,119 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:21,120 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:21,126 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:21,129 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:21,133 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 19:20:21,134 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] ()
2025-06-10 19:20:21,135 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:21,137 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (0, 20)
2025-06-10 19:20:21,139 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:21,140 - sqlalchemy.engine.Engine - INFO - [cached since 2696s ago] (1, 2, 3, 4)
2025-06-10 19:20:21,141 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:21,142 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:24,819 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:24,821 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:24,822 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:24,823 - sqlalchemy.engine.Engine - INFO - [generated in 0.00085s] ('PENDING',)
2025-06-10 19:20:24,825 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:24,825 - sqlalchemy.engine.Engine - INFO - [generated in 0.00077s] ('PENDING', 0, 20)
2025-06-10 19:20:24,827 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:24,827 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:24,833 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:24,834 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:24,835 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:24,836 - sqlalchemy.engine.Engine - INFO - [cached since 0.01388s ago] ('PENDING',)
2025-06-10 19:20:24,837 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:24,838 - sqlalchemy.engine.Engine - INFO - [cached since 0.01332s ago] ('PENDING', 0, 20)
2025-06-10 19:20:24,839 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:24,840 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:24,844 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:24,845 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:24,849 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:24,850 - sqlalchemy.engine.Engine - INFO - [cached since 0.02858s ago] ('PENDING',)
2025-06-10 19:20:24,853 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:24,854 - sqlalchemy.engine.Engine - INFO - [cached since 0.0291s ago] ('PENDING', 0, 20)
2025-06-10 19:20:24,855 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:24,856 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:24,861 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:24,862 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:24,865 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:24,866 - sqlalchemy.engine.Engine - INFO - [cached since 0.04478s ago] ('PENDING',)
2025-06-10 19:20:24,868 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:24,870 - sqlalchemy.engine.Engine - INFO - [cached since 0.04523s ago] ('PENDING', 0, 20)
2025-06-10 19:20:24,871 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:24,871 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:24,875 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:24,876 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:24,877 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:24,881 - sqlalchemy.engine.Engine - INFO - [cached since 0.05968s ago] ('PENDING',)
2025-06-10 19:20:24,884 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:24,885 - sqlalchemy.engine.Engine - INFO - [cached since 0.06038s ago] ('PENDING', 0, 20)
2025-06-10 19:20:24,887 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:24,887 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:24,893 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:24,894 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:24,899 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:24,900 - sqlalchemy.engine.Engine - INFO - [cached since 0.0783s ago] ('PENDING',)
2025-06-10 19:20:24,903 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:24,907 - sqlalchemy.engine.Engine - INFO - [cached since 0.08224s ago] ('PENDING', 0, 20)
2025-06-10 19:20:24,908 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:24,909 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:24,914 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:24,918 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:24,919 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:24,920 - sqlalchemy.engine.Engine - INFO - [cached since 0.09831s ago] ('PENDING',)
2025-06-10 19:20:24,925 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:24,926 - sqlalchemy.engine.Engine - INFO - [cached since 0.1014s ago] ('PENDING', 0, 20)
2025-06-10 19:20:24,927 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:24,928 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:24,933 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:24,938 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:24,939 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:24,940 - sqlalchemy.engine.Engine - INFO - [cached since 0.1182s ago] ('PENDING',)
2025-06-10 19:20:24,942 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:24,943 - sqlalchemy.engine.Engine - INFO - [cached since 0.1183s ago] ('PENDING', 0, 20)
2025-06-10 19:20:24,944 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:24,946 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:24,950 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:24,953 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:24,954 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:24,954 - sqlalchemy.engine.Engine - INFO - [cached since 0.1328s ago] ('PENDING',)
2025-06-10 19:20:24,956 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:24,957 - sqlalchemy.engine.Engine - INFO - [cached since 0.1323s ago] ('PENDING', 0, 20)
2025-06-10 19:20:24,958 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:24,958 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:24,966 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:24,967 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:24,968 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:24,969 - sqlalchemy.engine.Engine - INFO - [cached since 0.1473s ago] ('PENDING',)
2025-06-10 19:20:24,971 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:24,971 - sqlalchemy.engine.Engine - INFO - [cached since 0.1469s ago] ('PENDING', 0, 20)
2025-06-10 19:20:24,973 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:24,973 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:24,986 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:24,987 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:24,988 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:24,988 - sqlalchemy.engine.Engine - INFO - [cached since 0.1667s ago] ('PENDING',)
2025-06-10 19:20:24,990 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:24,991 - sqlalchemy.engine.Engine - INFO - [cached since 0.1662s ago] ('PENDING', 0, 20)
2025-06-10 19:20:24,992 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:24,992 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:24,998 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:25,001 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:25,004 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:25,005 - sqlalchemy.engine.Engine - INFO - [cached since 0.1829s ago] ('PENDING',)
2025-06-10 19:20:25,006 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:25,007 - sqlalchemy.engine.Engine - INFO - [cached since 0.1822s ago] ('PENDING', 0, 20)
2025-06-10 19:20:25,008 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:25,008 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:25,013 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:25,017 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:25,018 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:25,019 - sqlalchemy.engine.Engine - INFO - [cached since 0.1974s ago] ('PENDING',)
2025-06-10 19:20:25,021 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:25,021 - sqlalchemy.engine.Engine - INFO - [cached since 0.197s ago] ('PENDING', 0, 20)
2025-06-10 19:20:25,023 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:25,023 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:25,028 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:25,033 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:25,033 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:25,034 - sqlalchemy.engine.Engine - INFO - [cached since 0.2125s ago] ('PENDING',)
2025-06-10 19:20:25,036 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:25,037 - sqlalchemy.engine.Engine - INFO - [cached since 0.2123s ago] ('PENDING', 0, 20)
2025-06-10 19:20:25,038 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:25,038 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:25,042 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:25,044 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:25,046 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:25,052 - sqlalchemy.engine.Engine - INFO - [cached since 0.2306s ago] ('PENDING',)
2025-06-10 19:20:25,054 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:25,054 - sqlalchemy.engine.Engine - INFO - [cached since 0.23s ago] ('PENDING', 0, 20)
2025-06-10 19:20:25,056 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:25,056 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:25,061 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:25,066 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:25,066 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:25,067 - sqlalchemy.engine.Engine - INFO - [cached since 0.2453s ago] ('PENDING',)
2025-06-10 19:20:25,069 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:25,070 - sqlalchemy.engine.Engine - INFO - [cached since 0.2452s ago] ('PENDING', 0, 20)
2025-06-10 19:20:25,071 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:25,071 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:25,075 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:25,076 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:25,077 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:25,078 - sqlalchemy.engine.Engine - INFO - [cached since 0.2567s ago] ('PENDING',)
2025-06-10 19:20:25,083 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:25,085 - sqlalchemy.engine.Engine - INFO - [cached since 0.2601s ago] ('PENDING', 0, 20)
2025-06-10 19:20:25,086 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:25,087 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:25,092 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:25,093 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:25,094 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:25,099 - sqlalchemy.engine.Engine - INFO - [cached since 0.2772s ago] ('PENDING',)
2025-06-10 19:20:25,101 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:25,102 - sqlalchemy.engine.Engine - INFO - [cached since 0.2774s ago] ('PENDING', 0, 20)
2025-06-10 19:20:25,103 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:25,103 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:25,108 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:25,109 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:25,109 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:25,110 - sqlalchemy.engine.Engine - INFO - [cached since 0.2884s ago] ('PENDING',)
2025-06-10 19:20:25,112 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:25,118 - sqlalchemy.engine.Engine - INFO - [cached since 0.2936s ago] ('PENDING', 0, 20)
2025-06-10 19:20:25,121 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:25,121 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,034 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,035 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,036 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,037 - sqlalchemy.engine.Engine - INFO - [cached since 2.215s ago] ('ANALYZING',)
2025-06-10 19:20:27,038 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,039 - sqlalchemy.engine.Engine - INFO - [cached since 2.215s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,040 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,041 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,053 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,055 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,055 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,056 - sqlalchemy.engine.Engine - INFO - [cached since 2.234s ago] ('ANALYZING',)
2025-06-10 19:20:27,058 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,058 - sqlalchemy.engine.Engine - INFO - [cached since 2.234s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,060 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,060 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,070 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,072 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,073 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,074 - sqlalchemy.engine.Engine - INFO - [cached since 2.252s ago] ('ANALYZING',)
2025-06-10 19:20:27,075 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,084 - sqlalchemy.engine.Engine - INFO - [cached since 2.26s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,086 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,087 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,150 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,154 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,154 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,156 - sqlalchemy.engine.Engine - INFO - [cached since 2.334s ago] ('ANALYZING',)
2025-06-10 19:20:27,157 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,169 - sqlalchemy.engine.Engine - INFO - [cached since 2.344s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,171 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,171 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,187 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,189 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,190 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,191 - sqlalchemy.engine.Engine - INFO - [cached since 2.369s ago] ('ANALYZING',)
2025-06-10 19:20:27,193 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,202 - sqlalchemy.engine.Engine - INFO - [cached since 2.378s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,204 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,204 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,225 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,227 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,234 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,235 - sqlalchemy.engine.Engine - INFO - [cached since 2.414s ago] ('ANALYZING',)
2025-06-10 19:20:27,238 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,240 - sqlalchemy.engine.Engine - INFO - [cached since 2.416s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,242 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,243 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,270 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,275 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,276 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,290 - sqlalchemy.engine.Engine - INFO - [cached since 2.468s ago] ('ANALYZING',)
2025-06-10 19:20:27,291 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,300 - sqlalchemy.engine.Engine - INFO - [cached since 2.475s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,301 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,302 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,358 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,359 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,360 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,365 - sqlalchemy.engine.Engine - INFO - [cached since 2.544s ago] ('ANALYZING',)
2025-06-10 19:20:27,368 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,371 - sqlalchemy.engine.Engine - INFO - [cached since 2.546s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,372 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,385 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,392 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,393 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,400 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,401 - sqlalchemy.engine.Engine - INFO - [cached since 2.58s ago] ('ANALYZING',)
2025-06-10 19:20:27,404 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,405 - sqlalchemy.engine.Engine - INFO - [cached since 2.581s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,406 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,407 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,421 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,422 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,423 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,423 - sqlalchemy.engine.Engine - INFO - [cached since 2.601s ago] ('ANALYZING',)
2025-06-10 19:20:27,425 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,426 - sqlalchemy.engine.Engine - INFO - [cached since 2.602s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,428 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,430 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,438 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,439 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,440 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,440 - sqlalchemy.engine.Engine - INFO - [cached since 2.619s ago] ('ANALYZING',)
2025-06-10 19:20:27,442 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,443 - sqlalchemy.engine.Engine - INFO - [cached since 2.618s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,444 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,445 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,455 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,455 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,456 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,457 - sqlalchemy.engine.Engine - INFO - [cached since 2.635s ago] ('ANALYZING',)
2025-06-10 19:20:27,459 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,459 - sqlalchemy.engine.Engine - INFO - [cached since 2.635s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,462 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,463 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,471 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,472 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,473 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,473 - sqlalchemy.engine.Engine - INFO - [cached since 2.652s ago] ('ANALYZING',)
2025-06-10 19:20:27,476 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,476 - sqlalchemy.engine.Engine - INFO - [cached since 2.652s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,479 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,492 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,501 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,502 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,503 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,503 - sqlalchemy.engine.Engine - INFO - [cached since 2.681s ago] ('ANALYZING',)
2025-06-10 19:20:27,504 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,505 - sqlalchemy.engine.Engine - INFO - [cached since 2.681s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,507 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,507 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,517 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,519 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,519 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,520 - sqlalchemy.engine.Engine - INFO - [cached since 2.698s ago] ('ANALYZING',)
2025-06-10 19:20:27,522 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,523 - sqlalchemy.engine.Engine - INFO - [cached since 2.698s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,524 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,525 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,532 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,532 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,533 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,533 - sqlalchemy.engine.Engine - INFO - [cached since 2.712s ago] ('ANALYZING',)
2025-06-10 19:20:27,535 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,536 - sqlalchemy.engine.Engine - INFO - [cached since 2.711s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,537 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,537 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,542 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,542 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,543 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,544 - sqlalchemy.engine.Engine - INFO - [cached since 2.723s ago] ('ANALYZING',)
2025-06-10 19:20:27,550 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,552 - sqlalchemy.engine.Engine - INFO - [cached since 2.727s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,554 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,555 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,559 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,560 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,561 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,566 - sqlalchemy.engine.Engine - INFO - [cached since 2.745s ago] ('ANALYZING',)
2025-06-10 19:20:27,569 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,570 - sqlalchemy.engine.Engine - INFO - [cached since 2.746s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,572 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,572 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:27,576 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:27,577 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:27,577 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:27,578 - sqlalchemy.engine.Engine - INFO - [cached since 2.756s ago] ('ANALYZING',)
2025-06-10 19:20:27,580 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:27,584 - sqlalchemy.engine.Engine - INFO - [cached since 2.76s ago] ('ANALYZING', 0, 20)
2025-06-10 19:20:27,586 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:27,587 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,542 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,550 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,552 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,553 - sqlalchemy.engine.Engine - INFO - [cached since 4.731s ago] ('COMPLETED',)
2025-06-10 19:20:29,554 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,556 - sqlalchemy.engine.Engine - INFO - [cached since 4.731s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,559 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,559 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,568 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,569 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,570 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,571 - sqlalchemy.engine.Engine - INFO - [cached since 4.749s ago] ('COMPLETED',)
2025-06-10 19:20:29,572 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,573 - sqlalchemy.engine.Engine - INFO - [cached since 4.748s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,574 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,574 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,585 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,586 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,587 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,588 - sqlalchemy.engine.Engine - INFO - [cached since 4.766s ago] ('COMPLETED',)
2025-06-10 19:20:29,589 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,591 - sqlalchemy.engine.Engine - INFO - [cached since 4.766s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,592 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,592 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,603 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,604 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,605 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,605 - sqlalchemy.engine.Engine - INFO - [cached since 4.783s ago] ('COMPLETED',)
2025-06-10 19:20:29,606 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,607 - sqlalchemy.engine.Engine - INFO - [cached since 4.783s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,608 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,609 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,620 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,621 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,621 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,622 - sqlalchemy.engine.Engine - INFO - [cached since 4.8s ago] ('COMPLETED',)
2025-06-10 19:20:29,623 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,624 - sqlalchemy.engine.Engine - INFO - [cached since 4.8s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,625 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,626 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,634 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,636 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,637 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,638 - sqlalchemy.engine.Engine - INFO - [cached since 4.816s ago] ('COMPLETED',)
2025-06-10 19:20:29,639 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,640 - sqlalchemy.engine.Engine - INFO - [cached since 4.815s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,641 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,641 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,652 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,653 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,654 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,654 - sqlalchemy.engine.Engine - INFO - [cached since 4.833s ago] ('COMPLETED',)
2025-06-10 19:20:29,656 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,657 - sqlalchemy.engine.Engine - INFO - [cached since 4.832s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,658 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,658 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,668 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,669 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,670 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,670 - sqlalchemy.engine.Engine - INFO - [cached since 4.849s ago] ('COMPLETED',)
2025-06-10 19:20:29,672 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,672 - sqlalchemy.engine.Engine - INFO - [cached since 4.848s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,673 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,674 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,682 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,684 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,685 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,686 - sqlalchemy.engine.Engine - INFO - [cached since 4.864s ago] ('COMPLETED',)
2025-06-10 19:20:29,687 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,688 - sqlalchemy.engine.Engine - INFO - [cached since 4.863s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,689 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,690 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,693 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,695 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,700 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,701 - sqlalchemy.engine.Engine - INFO - [cached since 4.879s ago] ('COMPLETED',)
2025-06-10 19:20:29,702 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,703 - sqlalchemy.engine.Engine - INFO - [cached since 4.879s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,705 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,705 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,708 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,710 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,710 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,711 - sqlalchemy.engine.Engine - INFO - [cached since 4.889s ago] ('COMPLETED',)
2025-06-10 19:20:29,716 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,717 - sqlalchemy.engine.Engine - INFO - [cached since 4.893s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,719 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,719 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,723 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,724 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,724 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,725 - sqlalchemy.engine.Engine - INFO - [cached since 4.903s ago] ('COMPLETED',)
2025-06-10 19:20:29,726 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,727 - sqlalchemy.engine.Engine - INFO - [cached since 4.903s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,733 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,734 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,738 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,739 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,739 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,740 - sqlalchemy.engine.Engine - INFO - [cached since 4.918s ago] ('COMPLETED',)
2025-06-10 19:20:29,741 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,742 - sqlalchemy.engine.Engine - INFO - [cached since 4.917s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,743 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,743 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,755 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,756 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,757 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,757 - sqlalchemy.engine.Engine - INFO - [cached since 4.936s ago] ('COMPLETED',)
2025-06-10 19:20:29,759 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,759 - sqlalchemy.engine.Engine - INFO - [cached since 4.935s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,761 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,765 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,771 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,772 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,772 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,773 - sqlalchemy.engine.Engine - INFO - [cached since 4.951s ago] ('COMPLETED',)
2025-06-10 19:20:29,774 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,775 - sqlalchemy.engine.Engine - INFO - [cached since 4.95s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,776 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,776 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,787 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,788 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,789 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,789 - sqlalchemy.engine.Engine - INFO - [cached since 4.967s ago] ('COMPLETED',)
2025-06-10 19:20:29,791 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,791 - sqlalchemy.engine.Engine - INFO - [cached since 4.967s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,792 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,793 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,804 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,805 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,806 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,806 - sqlalchemy.engine.Engine - INFO - [cached since 4.984s ago] ('COMPLETED',)
2025-06-10 19:20:29,808 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,808 - sqlalchemy.engine.Engine - INFO - [cached since 4.984s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,810 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,812 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,821 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,822 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,823 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,824 - sqlalchemy.engine.Engine - INFO - [cached since 5.002s ago] ('COMPLETED',)
2025-06-10 19:20:29,825 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,827 - sqlalchemy.engine.Engine - INFO - [cached since 5.002s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,830 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,834 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:29,839 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:29,840 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:29,840 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:29,841 - sqlalchemy.engine.Engine - INFO - [cached since 5.019s ago] ('COMPLETED',)
2025-06-10 19:20:29,843 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:29,843 - sqlalchemy.engine.Engine - INFO - [cached since 5.019s ago] ('COMPLETED', 0, 20)
2025-06-10 19:20:29,846 - app.services.requirement_service - INFO -  获取需求列表成功: 返回0条记录，总数=0
2025-06-10 19:20:29,851 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,385 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,386 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,387 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,388 - sqlalchemy.engine.Engine - INFO - [cached since 6.566s ago] ('DRAFT',)
2025-06-10 19:20:31,390 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,391 - sqlalchemy.engine.Engine - INFO - [cached since 6.567s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,394 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,398 - sqlalchemy.engine.Engine - INFO - [cached since 2706s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,400 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,400 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,454 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,460 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,463 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,467 - sqlalchemy.engine.Engine - INFO - [cached since 6.646s ago] ('DRAFT',)
2025-06-10 19:20:31,469 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,471 - sqlalchemy.engine.Engine - INFO - [cached since 6.646s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,473 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,480 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,483 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,484 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,522 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,527 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,535 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,539 - sqlalchemy.engine.Engine - INFO - [cached since 6.717s ago] ('DRAFT',)
2025-06-10 19:20:31,542 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,550 - sqlalchemy.engine.Engine - INFO - [cached since 6.725s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,552 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,554 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,556 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,556 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,602 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,604 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,605 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,607 - sqlalchemy.engine.Engine - INFO - [cached since 6.785s ago] ('DRAFT',)
2025-06-10 19:20:31,612 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,613 - sqlalchemy.engine.Engine - INFO - [cached since 6.788s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,615 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,617 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,618 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,619 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,626 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,632 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,633 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,634 - sqlalchemy.engine.Engine - INFO - [cached since 6.812s ago] ('DRAFT',)
2025-06-10 19:20:31,636 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,637 - sqlalchemy.engine.Engine - INFO - [cached since 6.813s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,639 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,641 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,642 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,643 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,654 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,655 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,656 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,657 - sqlalchemy.engine.Engine - INFO - [cached since 6.835s ago] ('DRAFT',)
2025-06-10 19:20:31,658 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,659 - sqlalchemy.engine.Engine - INFO - [cached since 6.835s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,662 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,668 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,670 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,671 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,676 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,678 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,680 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,687 - sqlalchemy.engine.Engine - INFO - [cached since 6.865s ago] ('DRAFT',)
2025-06-10 19:20:31,690 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,691 - sqlalchemy.engine.Engine - INFO - [cached since 6.867s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,693 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,695 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,697 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,697 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,703 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,704 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,706 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,706 - sqlalchemy.engine.Engine - INFO - [cached since 6.885s ago] ('DRAFT',)
2025-06-10 19:20:31,708 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,709 - sqlalchemy.engine.Engine - INFO - [cached since 6.884s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,711 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,717 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,719 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,719 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,725 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,727 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,727 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,728 - sqlalchemy.engine.Engine - INFO - [cached since 6.906s ago] ('DRAFT',)
2025-06-10 19:20:31,729 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,734 - sqlalchemy.engine.Engine - INFO - [cached since 6.91s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,737 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,740 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,742 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,742 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,747 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,750 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,752 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,754 - sqlalchemy.engine.Engine - INFO - [cached since 6.932s ago] ('DRAFT',)
2025-06-10 19:20:31,755 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,756 - sqlalchemy.engine.Engine - INFO - [cached since 6.932s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,759 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,763 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,765 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,765 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,773 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,774 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,775 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,776 - sqlalchemy.engine.Engine - INFO - [cached since 6.954s ago] ('DRAFT',)
2025-06-10 19:20:31,778 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,783 - sqlalchemy.engine.Engine - INFO - [cached since 6.958s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,787 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,789 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,791 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,791 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,799 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,800 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,801 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,802 - sqlalchemy.engine.Engine - INFO - [cached since 6.981s ago] ('DRAFT',)
2025-06-10 19:20:31,804 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,804 - sqlalchemy.engine.Engine - INFO - [cached since 6.98s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,806 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,808 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,809 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,810 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,821 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,823 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,824 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,825 - sqlalchemy.engine.Engine - INFO - [cached since 7.003s ago] ('DRAFT',)
2025-06-10 19:20:31,827 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,834 - sqlalchemy.engine.Engine - INFO - [cached since 7.01s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,837 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,840 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,842 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,842 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,851 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,854 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,855 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,855 - sqlalchemy.engine.Engine - INFO - [cached since 7.033s ago] ('DRAFT',)
2025-06-10 19:20:31,857 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,858 - sqlalchemy.engine.Engine - INFO - [cached since 7.034s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,860 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,863 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,869 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,870 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,876 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,877 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,878 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,880 - sqlalchemy.engine.Engine - INFO - [cached since 7.058s ago] ('DRAFT',)
2025-06-10 19:20:31,886 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,892 - sqlalchemy.engine.Engine - INFO - [cached since 7.067s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,894 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,896 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,898 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,900 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,905 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,907 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,907 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,908 - sqlalchemy.engine.Engine - INFO - [cached since 7.086s ago] ('DRAFT',)
2025-06-10 19:20:31,909 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,910 - sqlalchemy.engine.Engine - INFO - [cached since 7.085s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,912 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,919 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,920 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,921 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,927 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,930 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,933 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,934 - sqlalchemy.engine.Engine - INFO - [cached since 7.113s ago] ('DRAFT',)
2025-06-10 19:20:31,937 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,939 - sqlalchemy.engine.Engine - INFO - [cached since 7.115s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,942 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,943 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,945 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,947 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,952 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,953 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,955 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,956 - sqlalchemy.engine.Engine - INFO - [cached since 7.134s ago] ('DRAFT',)
2025-06-10 19:20:31,958 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,958 - sqlalchemy.engine.Engine - INFO - [cached since 7.134s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,960 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,967 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,968 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:31,969 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 19:20:31,977 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 19:20:31,980 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 19:20:31,984 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements 
WHERE requirements.status = %s
2025-06-10 19:20:31,987 - sqlalchemy.engine.Engine - INFO - [cached since 7.165s ago] ('DRAFT',)
2025-06-10 19:20:31,989 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements 
WHERE requirements.status = %s ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 19:20:31,990 - sqlalchemy.engine.Engine - INFO - [cached since 7.166s ago] ('DRAFT', 0, 20)
2025-06-10 19:20:31,992 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.req_id AS ai_analysis_results_req_id, ai_analysis_results.id AS ai_analysis_results_id, ai_analysis_results.analysis_result AS ai_analysis_results_analysis_result, ai_analysis_results.structured_result AS ai_analysis_results_structured_result, ai_analysis_results.key_points AS ai_analysis_results_key_points, ai_analysis_results.risk_assessment AS ai_analysis_results_risk_assessment, ai_analysis_results.suggestions AS ai_analysis_results_suggestions, ai_analysis_results.manual_adjust AS ai_analysis_results_manual_adjust, ai_analysis_results.is_manually_adjusted AS ai_analysis_results_is_manually_adjusted, ai_analysis_results.adjusted_by AS ai_analysis_results_adjusted_by, ai_analysis_results.adjusted_time AS ai_analysis_results_adjusted_time, ai_analysis_results.ai_model AS ai_analysis_results_ai_model, ai_analysis_results.ai_version AS ai_analysis_results_ai_version, ai_analysis_results.analysis_config AS ai_analysis_results_analysis_config, ai_analysis_results.analysis_status AS ai_analysis_results_analysis_status, ai_analysis_results.error_message AS ai_analysis_results_error_message, ai_analysis_results.processing_time AS ai_analysis_results_processing_time, ai_analysis_results.created_time AS ai_analysis_results_created_time, ai_analysis_results.update_time AS ai_analysis_results_update_time 
FROM ai_analysis_results 
WHERE ai_analysis_results.req_id IN (%s, %s, %s, %s)
2025-06-10 19:20:31,996 - sqlalchemy.engine.Engine - INFO - [cached since 2707s ago] (4, 3, 2, 1)
2025-06-10 19:20:31,998 - app.services.requirement_service - INFO -  获取需求列表成功: 返回4条记录，总数=4
2025-06-10 19:20:32,000 - sqlalchemy.engine.Engine - INFO - ROLLBACK
