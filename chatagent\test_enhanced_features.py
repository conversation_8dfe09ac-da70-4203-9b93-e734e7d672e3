#!/usr/bin/env python3
"""
测试增强功能：文件上传、多轮对话上下文、用户会话管理
"""

import sys
import os
import asyncio
import json
from pathlib import Path

# 添加backend目录到路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

async def test_session_management():
    """测试会话管理功能"""
    print("🧪 测试会话管理功能")
    print("=" * 40)
    
    try:
        from session_manager import session_manager
        
        # 测试创建会话
        print("📝 测试创建会话...")
        session_id = session_manager.create_session("test_user")
        print(f"✅ 会话创建成功: {session_id}")
        
        # 测试获取会话
        print("📝 测试获取会话...")
        session = session_manager.get_session(session_id)
        print(f"✅ 会话获取成功: {session['user_id']}")
        
        # 测试添加消息
        print("📝 测试添加消息...")
        session_manager.add_message(session_id, "user", "你好，这是第一条消息")
        session_manager.add_message(session_id, "assistant", "你好！很高兴见到你。")
        session_manager.add_message(session_id, "user", "请介绍一下你自己")
        session_manager.add_message(session_id, "assistant", "我是一个AI助手，可以帮助您解答问题。")
        
        # 测试获取对话上下文
        print("📝 测试获取对话上下文...")
        context = session_manager.get_conversation_context(session_id)
        print(f"✅ 对话上下文获取成功，共 {len(context)} 条消息")
        
        for msg in context:
            print(f"  {msg['role']}: {msg['content'][:30]}...")
        
        # 测试会话统计
        print("📝 测试会话统计...")
        stats = session_manager.get_session_stats()
        print(f"✅ 会话统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 会话管理测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

async def test_file_upload():
    """测试文件上传功能"""
    print("\n🧪 测试文件上传功能")
    print("=" * 40)
    
    try:
        from session_manager import session_manager
        
        # 创建测试会话
        session_id = session_manager.create_session("file_test_user")
        
        # 创建测试文件
        test_files = [
            ("test.txt", "这是一个测试文本文件的内容。\n包含多行文本。", "text/plain"),
            ("test.json", '{"name": "测试", "type": "JSON文件", "data": [1, 2, 3]}', "application/json"),
            ("test.md", "# 测试Markdown文件\n\n这是一个**测试**文件。", "text/markdown")
        ]
        
        uploaded_file_ids = []
        
        for filename, content, content_type in test_files:
            print(f"📝 测试上传文件: {filename}")
            
            file_data = content.encode('utf-8')
            file_id = session_manager.upload_file(
                session_id=session_id,
                file_data=file_data,
                filename=filename,
                content_type=content_type
            )
            
            if file_id:
                uploaded_file_ids.append(file_id)
                print(f"✅ 文件上传成功: {filename} -> {file_id}")
                
                # 获取文件信息
                file_info = session_manager.get_file(file_id)
                print(f"  文件类型: {file_info['file_type']}")
                print(f"  文件大小: {file_info['file_size']} 字节")
            else:
                print(f"❌ 文件上传失败: {filename}")
        
        # 测试带文件的消息
        print("📝 测试带文件的消息...")
        session_manager.add_message(
            session_id, 
            "user", 
            "请分析这些上传的文件", 
            uploaded_file_ids
        )
        
        # 获取会话文件列表
        session = session_manager.get_session(session_id)
        print(f"✅ 会话文件数量: {len(session['uploaded_files'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件上传测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

async def test_multi_turn_conversation():
    """测试多轮对话上下文"""
    print("\n🧪 测试多轮对话上下文")
    print("=" * 40)
    
    try:
        from chat_service import ChatService
        from session_manager import session_manager
        
        # 初始化聊天服务
        chat_service = ChatService()
        if not chat_service.is_initialized:
            print("❌ 聊天服务初始化失败")
            return False
        
        # 创建测试会话
        session_id = session_manager.create_session("context_test_user")
        
        # 模拟多轮对话
        conversations = [
            "我叫张三，今年25岁",
            "我的爱好是编程和阅读",
            "请问你还记得我的名字吗？",
            "我的年龄是多少？",
            "我有什么爱好？"
        ]
        
        print("📝 开始多轮对话测试...")
        
        for i, message in enumerate(conversations, 1):
            print(f"\n轮次 {i}: {message}")
            
            # 使用流式对话
            response_parts = []
            async for chunk in chat_service.chat_stream(message, session_id):
                try:
                    data = json.loads(chunk)
                    if data.get("type") == "chunk":
                        content = data.get("data", {}).get("content", "")
                        response_parts.append(content)
                    elif data.get("type") == "complete":
                        full_response = data.get("data", {}).get("full_response", "")
                        if full_response and not response_parts:
                            response_parts = [full_response]
                        break
                    elif data.get("type") == "error":
                        error_msg = data.get("data", {}).get("message", "未知错误")
                        print(f"❌ 错误: {error_msg}")
                        break
                except json.JSONDecodeError:
                    continue
            
            response = "".join(response_parts)
            print(f"AI回复: {response[:100]}{'...' if len(response) > 100 else ''}")
        
        # 检查对话历史
        context = session_manager.get_conversation_context(session_id)
        print(f"\n✅ 多轮对话测试完成，共 {len(context)} 条消息")
        
        return True
        
    except Exception as e:
        print(f"❌ 多轮对话测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

async def test_integrated_features():
    """测试集成功能：文件上传 + 多轮对话"""
    print("\n🧪 测试集成功能")
    print("=" * 40)
    
    try:
        from chat_service import ChatService
        from session_manager import session_manager
        
        # 初始化聊天服务
        chat_service = ChatService()
        
        # 创建测试会话
        session_id = session_manager.create_session("integrated_test_user")
        
        # 上传测试文件
        test_content = """
        用户信息：
        姓名：李四
        年龄：30岁
        职业：软件工程师
        技能：Python, JavaScript, AI开发
        """
        
        file_id = session_manager.upload_file(
            session_id=session_id,
            file_data=test_content.encode('utf-8'),
            filename="user_profile.txt",
            content_type="text/plain"
        )
        
        print(f"✅ 测试文件上传成功: {file_id}")
        
        # 带文件的对话
        messages_with_files = [
            ("请分析这个用户档案文件", [file_id]),
            ("这个用户适合什么样的工作？", []),
            ("根据文件信息，推荐一些学习方向", [])
        ]
        
        for message, file_ids in messages_with_files:
            print(f"\n📝 消息: {message}")
            if file_ids:
                print(f"📁 附件: {len(file_ids)} 个文件")
            
            # 流式对话
            response_parts = []
            async for chunk in chat_service.chat_stream(message, session_id, file_ids):
                try:
                    data = json.loads(chunk)
                    if data.get("type") == "complete":
                        full_response = data.get("data", {}).get("full_response", "")
                        response_parts = [full_response]
                        break
                except json.JSONDecodeError:
                    continue
            
            response = "".join(response_parts)
            print(f"🤖 AI回复: {response[:150]}{'...' if len(response) > 150 else ''}")
        
        print("\n✅ 集成功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 集成功能测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

async def main():
    """主测试函数"""
    print("🤖 智能聊天系统 - 增强功能测试")
    print("测试：文件上传、多轮对话上下文、用户会话管理")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("会话管理", test_session_management),
        ("文件上传", test_file_upload),
        ("多轮对话", test_multi_turn_conversation),
        ("集成功能", test_integrated_features),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = await test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 增强功能测试结果汇总:")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 所有增强功能测试通过！")
        print("💡 新功能已就绪：")
        print("   📁 文件上传功能")
        print("   🔄 多轮对话上下文")
        print("   👤 用户会话管理")
    else:
        print("⚠️ 部分功能测试失败，请检查实现。")
    
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
