"""
聊天服务模块
基于AutoGen 0.7.6 + DeepSeek实现智能对话
"""

import json
import asyncio
from typing import AsyncGenerator, Dict, Any
from datetime import datetime
import traceback

# AutoGen 0.7.6 导入
try:
    import autogen
    from autogen import AssistantAgent, UserProxyAgent
    # 尝试导入配置相关函数
    try:
        from autogen import config_list_from_json
    except ImportError:
        config_list_from_json = None

    AUTOGEN_AVAILABLE = True
    print(f"✅ AutoGen 导入成功，版本: {autogen.__version__}")
except ImportError as e:
    AUTOGEN_AVAILABLE = False
    print(f"❌ AutoGen导入失败: {e}")
    print("请安装AutoGen: pip install pyautogen==0.7.6")

from config import get_deepseek_config, validate_api_key, AI_CONFIG
from session_manager import session_manager


class ChatService:
    """
    智能聊天服务类
    使用AutoGen 0.7.6 + DeepSeek实现真实AI对话
    """
    
    def __init__(self):
        """初始化聊天服务"""
        self.assistant_agent = None
        self.user_proxy = None
        self.conversation_history = []
        self.is_initialized = False
        
        if not validate_api_key():
            raise ValueError("❌ DeepSeek API密钥未配置或无效")
            
        if AUTOGEN_AVAILABLE:
            self._initialize_agents()
        else:
            raise ImportError("❌ AutoGen 0.7.6不可用，请安装: pip install pyautogen==0.7.6")
    
    def _initialize_agents(self):
        """初始化AutoGen代理"""
        try:
            print("🚀 初始化AutoGen代理...")

            # 获取DeepSeek配置 - AutoGen 0.7.6格式
            llm_config = get_deepseek_config()
            print(f"📋 使用配置: {json.dumps(llm_config, indent=2, ensure_ascii=False)}")

            # 创建智能助手代理 - AutoGen 0.7.6语法
            self.assistant_agent = AssistantAgent(
                name="智能助手",
                llm_config=llm_config,
                system_message="""你是一个友善、专业的AI助手。你的特点：

🎯 **核心能力**：
- 提供准确、有用的信息和建议
- 进行自然、流畅的对话交流
- 帮助解决各种问题和疑问
- 支持多领域知识咨询

💡 **对话风格**：
- 友好亲切，富有同理心
- 回答简洁明了，重点突出
- 根据用户需求调整详细程度
- 适当使用emoji增加亲和力

🔧 **服务原则**：
- 诚实可靠，不编造信息
- 尊重用户隐私和观点
- 提供建设性的帮助和建议
- 保持专业和礼貌的态度

请用中文与用户进行自然对话，提供高质量的帮助和服务。"""
            )

            # 创建用户代理（用于管理对话流程）- AutoGen 0.7.6语法
            # 简化配置，避免复杂的对话流程
            self.user_proxy = UserProxyAgent(
                name="用户",
                human_input_mode="NEVER",  # 不需要人工输入
                max_consecutive_auto_reply=0,  # 禁用自动回复，避免循环
                code_execution_config=False,  # 禁用代码执行
                llm_config=False,  # 用户代理不需要LLM配置
                is_termination_msg=lambda x: True,  # 立即终止对话
            )

            self.is_initialized = True
            print("✅ AutoGen代理初始化成功")

        except Exception as e:
            print(f"❌ AutoGen代理初始化失败: {e}")
            print(f"详细错误: {traceback.format_exc()}")
            raise
    
    async def chat_stream(self, message: str, session_id: str = None,
                         file_ids: list = None) -> AsyncGenerator[str, None]:
        """
        流式聊天对话（支持会话管理和文件上传）

        Args:
            message: 用户消息
            session_id: 会话ID（可选）
            file_ids: 上传文件ID列表（可选）

        Yields:
            str: 流式响应数据（JSON格式）
        """
        if not self.is_initialized:
            yield self._format_error("聊天服务未初始化")
            return

        # 处理会话管理
        if not session_id:
            session_id = session_manager.create_session()
            yield self._format_stream_data("session_created", {"session_id": session_id})

        # 验证会话
        session = session_manager.get_session(session_id)
        if not session:
            session_id = session_manager.create_session()
            session = session_manager.get_session(session_id)
            yield self._format_stream_data("session_created", {"session_id": session_id})

        try:
            # 处理文件信息
            file_context = ""
            if file_ids:
                file_context = self._process_uploaded_files(file_ids)
                if file_context:
                    message = f"{message}\n\n[附件信息]\n{file_context}"

            # 添加用户消息到会话
            session_manager.add_message(session_id, "user", message, file_ids)

            # 记录用户消息（保持向后兼容）
            user_msg = {
                "role": "user",
                "content": message,
                "timestamp": datetime.now().isoformat()
            }
            self.conversation_history.append(user_msg)
            
            # 发送开始信号
            yield self._format_stream_data("start", {"message": "AI正在思考中..."})
            
            # 使用AutoGen进行对话（支持多轮上下文）
            print(f"🤖 用户消息: {message}")

            # 在线程池中执行AutoGen对话（避免阻塞）
            response = await asyncio.to_thread(
                self._get_autogen_response,
                message, session_id
            )
            
            if response:
                # 添加AI回复到会话
                session_manager.add_message(session_id, "assistant", response)

                # 记录AI回复（保持向后兼容）
                ai_msg = {
                    "role": "assistant",
                    "content": response,
                    "timestamp": datetime.now().isoformat()
                }
                self.conversation_history.append(ai_msg)
                
                # 模拟流式输出（将完整回复分块发送）
                chunks = self._split_response(response)
                for i, chunk in enumerate(chunks):
                    yield self._format_stream_data("chunk", {
                        "content": chunk,
                        "index": i,
                        "total": len(chunks)
                    })
                    # 添加小延迟模拟真实流式效果
                    await asyncio.sleep(0.1)
                
                # 发送完成信号
                yield self._format_stream_data("complete", {
                    "full_response": response,
                    "message_count": len(self.conversation_history)
                })
            else:
                yield self._format_error("AI回复为空")
                
        except Exception as e:
            error_msg = f"聊天处理失败: {str(e)}"
            print(f"❌ {error_msg}")
            print(f"详细错误: {traceback.format_exc()}")
            yield self._format_error(error_msg)
    
    def _get_autogen_response(self, message: str, session_id: str = None) -> str:
        """获取AI回复（直接调用DeepSeek API，避免AutoGen复杂对话流程）"""
        try:
            # 方法1：尝试使用AutoGen的简单调用
            try:
                # 直接调用助手代理生成回复，避免复杂的对话流程
                response = self.assistant_agent.generate_reply(
                    messages=[{"role": "user", "content": message}]
                )

                if response and isinstance(response, str):
                    return response
                elif response and hasattr(response, 'content'):
                    return response.content
                elif response and isinstance(response, dict):
                    return response.get('content', str(response))

            except Exception as autogen_error:
                print(f"⚠️ AutoGen调用失败，尝试直接API调用: {autogen_error}")

            # 方法2：直接调用DeepSeek API（备用方案）
            return self._direct_deepseek_call(message, session_id)

        except Exception as e:
            print(f"❌ AI回复生成失败: {e}")
            print(f"详细错误: {traceback.format_exc()}")
            return f"对话出现错误: {str(e)}"

    def _direct_deepseek_call(self, message: str, session_id: str = None) -> str:
        """直接调用DeepSeek API"""
        try:
            import httpx
            from config import DEEPSEEK_API_KEY, DEEPSEEK_BASE_URL

            headers = {
                "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
                "Content-Type": "application/json"
            }

            # 构建对话历史
            messages = []

            # 添加系统消息
            messages.append({
                "role": "system",
                "content": """你是一个友善、专业的AI助手。你的特点：

🎯 **核心能力**：
- 提供准确、有用的信息和建议
- 进行自然、流畅的对话交流
- 帮助解决各种问题和疑问
- 支持多领域知识咨询

💡 **对话风格**：
- 友好亲切，富有同理心
- 回答简洁明了，重点突出
- 根据用户需求调整详细程度
- 适当使用emoji增加亲和力

🔧 **服务原则**：
- 诚实可靠，不编造信息
- 尊重用户隐私和观点
- 提供建设性的帮助和建议
- 保持专业和礼貌的态度

请用中文与用户进行自然对话，提供高质量的帮助和服务。"""
            })

            # 获取会话上下文（优先使用会话管理器的历史）
            if session_id:
                session_context = session_manager.get_conversation_context(session_id, include_files=False)
                print(f"🔍 获取到会话上下文: {len(session_context)} 条消息")

                # 转换为API格式，排除当前消息（最后一条）
                for msg in session_context[:-1]:  # 排除最后一条（当前用户消息）
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
                    print(f"📝 添加历史消息: {msg['role']} - {msg['content'][:50]}...")

                print(f"📊 最终发送给AI的消息数: {len(messages)} 条")
            else:
                # 备用：使用本地历史（最多5轮）
                recent_history = self.conversation_history[-10:] if len(self.conversation_history) > 10 else self.conversation_history
                for msg in recent_history:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })

            # 添加当前用户消息
            messages.append({
                "role": "user",
                "content": message
            })

            # 调用DeepSeek API
            data = {
                "model": "deepseek-chat",
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 2000,
                "stream": False
            }

            with httpx.Client(timeout=30.0) as client:
                response = client.post(
                    f"{DEEPSEEK_BASE_URL}/chat/completions",
                    headers=headers,
                    json=data
                )

                if response.status_code == 200:
                    result = response.json()
                    if 'choices' in result and len(result['choices']) > 0:
                        content = result['choices'][0]['message']['content']
                        print(f"✅ DeepSeek API调用成功")
                        return content
                    else:
                        return "抱歉，AI回复格式异常。"
                else:
                    print(f"❌ DeepSeek API错误: {response.status_code} - {response.text}")
                    return f"API调用失败: {response.status_code}"

        except Exception as e:
            print(f"❌ 直接API调用失败: {e}")
            return f"API调用出现错误: {str(e)}"
    
    def _split_response(self, response: str, chunk_size: int = 50) -> list:
        """将回复分割成块以模拟流式输出"""
        if not response:
            return [""]
            
        # 按字符分割，保持中文完整性
        chunks = []
        current_chunk = ""
        
        for char in response:
            current_chunk += char
            if len(current_chunk) >= chunk_size and char in "。！？\n ":
                chunks.append(current_chunk)
                current_chunk = ""
        
        # 添加剩余内容
        if current_chunk:
            chunks.append(current_chunk)
            
        return chunks if chunks else [response]
    
    def _format_stream_data(self, event_type: str, data: Dict[str, Any]) -> str:
        """格式化流式数据"""
        return json.dumps({
            "type": event_type,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }, ensure_ascii=False)
    
    def _format_error(self, error_message: str) -> str:
        """格式化错误信息"""
        return json.dumps({
            "type": "error",
            "data": {"message": error_message},
            "timestamp": datetime.now().isoformat()
        }, ensure_ascii=False)
    
    def get_conversation_history(self) -> list:
        """获取对话历史"""
        return self.conversation_history.copy()
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history.clear()
        if self.user_proxy and self.assistant_agent:
            # 清空AutoGen对话历史
            self.user_proxy.chat_messages.clear()
        print("🧹 对话历史已清空")

    def _process_uploaded_files(self, file_ids: list) -> str:
        """
        处理上传的文件，生成文件描述

        Args:
            file_ids: 文件ID列表

        Returns:
            str: 文件描述文本
        """
        if not file_ids:
            return ""

        file_descriptions = []

        for file_id in file_ids:
            file_info = session_manager.get_file(file_id)
            if not file_info:
                continue

            file_desc = f"文件名: {file_info['original_name']}\n"
            file_desc += f"文件类型: {file_info['file_type']}\n"
            file_desc += f"文件大小: {self._format_file_size(file_info['file_size'])}\n"

            # 根据文件类型处理内容
            if file_info['file_type'] == 'text':
                try:
                    import base64
                    content = base64.b64decode(file_info['base64_content']).decode('utf-8')
                    file_desc += f"文件内容:\n{content[:1000]}{'...' if len(content) > 1000 else ''}\n"
                except:
                    file_desc += "文件内容: 无法读取文本内容\n"

            elif file_info['file_type'] == 'image':
                file_desc += "文件内容: 这是一张图片文件\n"
                # 这里可以集成图像识别API

            elif file_info['file_type'] == 'json':
                try:
                    import base64
                    content = base64.b64decode(file_info['base64_content']).decode('utf-8')
                    import json
                    json_data = json.loads(content)
                    file_desc += f"JSON结构: {json.dumps(json_data, ensure_ascii=False, indent=2)[:500]}{'...' if len(str(json_data)) > 500 else ''}\n"
                except:
                    file_desc += "文件内容: 无法解析JSON格式\n"

            else:
                file_desc += f"文件内容: {file_info['file_type']}类型文件\n"

            file_descriptions.append(file_desc)

        return "\n---\n".join(file_descriptions)

    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
