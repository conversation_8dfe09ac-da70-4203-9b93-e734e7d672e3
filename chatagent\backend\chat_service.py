"""
聊天服务模块
基于AutoGen 0.7.6 + DeepSeek实现智能对话
"""

import json
import asyncio
from typing import AsyncGenerator, Dict, Any
from datetime import datetime
import traceback

# AutoGen 0.7.6 导入
try:
    import autogen
    from autogen import AssistantAgent, UserProxyAgent
    # 尝试导入配置相关函数
    try:
        from autogen import config_list_from_json
    except ImportError:
        config_list_from_json = None

    AUTOGEN_AVAILABLE = True
    print(f"✅ AutoGen 导入成功，版本: {autogen.__version__}")
except ImportError as e:
    AUTOGEN_AVAILABLE = False
    print(f"❌ AutoGen导入失败: {e}")
    print("请安装AutoGen: pip install pyautogen==0.7.6")

from config import get_deepseek_config, validate_api_key, AI_CONFIG


class ChatService:
    """
    智能聊天服务类
    使用AutoGen 0.7.6 + DeepSeek实现真实AI对话
    """
    
    def __init__(self):
        """初始化聊天服务"""
        self.assistant_agent = None
        self.user_proxy = None
        self.conversation_history = []
        self.is_initialized = False
        
        if not validate_api_key():
            raise ValueError("❌ DeepSeek API密钥未配置或无效")
            
        if AUTOGEN_AVAILABLE:
            self._initialize_agents()
        else:
            raise ImportError("❌ AutoGen 0.7.6不可用，请安装: pip install pyautogen==0.7.6")
    
    def _initialize_agents(self):
        """初始化AutoGen代理"""
        try:
            print("🚀 初始化AutoGen代理...")

            # 获取DeepSeek配置 - AutoGen 0.7.6格式
            llm_config = get_deepseek_config()
            print(f"📋 使用配置: {json.dumps(llm_config, indent=2, ensure_ascii=False)}")

            # 创建智能助手代理 - AutoGen 0.7.6语法
            self.assistant_agent = AssistantAgent(
                name="智能助手",
                llm_config=llm_config,
                system_message="""你是一个友善、专业的AI助手。你的特点：

🎯 **核心能力**：
- 提供准确、有用的信息和建议
- 进行自然、流畅的对话交流
- 帮助解决各种问题和疑问
- 支持多领域知识咨询

💡 **对话风格**：
- 友好亲切，富有同理心
- 回答简洁明了，重点突出
- 根据用户需求调整详细程度
- 适当使用emoji增加亲和力

🔧 **服务原则**：
- 诚实可靠，不编造信息
- 尊重用户隐私和观点
- 提供建设性的帮助和建议
- 保持专业和礼貌的态度

请用中文与用户进行自然对话，提供高质量的帮助和服务。"""
            )

            # 创建用户代理（用于管理对话流程）- AutoGen 0.7.6语法
            self.user_proxy = UserProxyAgent(
                name="用户",
                human_input_mode="NEVER",  # 不需要人工输入
                max_consecutive_auto_reply=1,  # 限制自动回复次数
                code_execution_config={"use_docker": False},  # 禁用代码执行
                llm_config=False,  # 用户代理不需要LLM配置
            )

            self.is_initialized = True
            print("✅ AutoGen代理初始化成功")

        except Exception as e:
            print(f"❌ AutoGen代理初始化失败: {e}")
            print(f"详细错误: {traceback.format_exc()}")
            raise
    
    async def chat_stream(self, message: str, session_id: str = None) -> AsyncGenerator[str, None]:
        """
        流式聊天对话
        
        Args:
            message: 用户消息
            session_id: 会话ID（可选）
            
        Yields:
            str: 流式响应数据（JSON格式）
        """
        if not self.is_initialized:
            yield self._format_error("聊天服务未初始化")
            return
            
        try:
            # 记录用户消息
            user_msg = {
                "role": "user",
                "content": message,
                "timestamp": datetime.now().isoformat()
            }
            self.conversation_history.append(user_msg)
            
            # 发送开始信号
            yield self._format_stream_data("start", {"message": "AI正在思考中..."})
            
            # 使用AutoGen进行对话
            print(f"🤖 用户消息: {message}")
            
            # 在线程池中执行AutoGen对话（避免阻塞）
            response = await asyncio.to_thread(
                self._get_autogen_response, 
                message
            )
            
            if response:
                # 记录AI回复
                ai_msg = {
                    "role": "assistant", 
                    "content": response,
                    "timestamp": datetime.now().isoformat()
                }
                self.conversation_history.append(ai_msg)
                
                # 模拟流式输出（将完整回复分块发送）
                chunks = self._split_response(response)
                for i, chunk in enumerate(chunks):
                    yield self._format_stream_data("chunk", {
                        "content": chunk,
                        "index": i,
                        "total": len(chunks)
                    })
                    # 添加小延迟模拟真实流式效果
                    await asyncio.sleep(0.1)
                
                # 发送完成信号
                yield self._format_stream_data("complete", {
                    "full_response": response,
                    "message_count": len(self.conversation_history)
                })
            else:
                yield self._format_error("AI回复为空")
                
        except Exception as e:
            error_msg = f"聊天处理失败: {str(e)}"
            print(f"❌ {error_msg}")
            print(f"详细错误: {traceback.format_exc()}")
            yield self._format_error(error_msg)
    
    def _get_autogen_response(self, message: str) -> str:
        """获取AutoGen回复（同步方法）- AutoGen 0.7.6兼容"""
        try:
            # 使用用户代理发起对话 - AutoGen 0.7.6语法
            chat_result = self.user_proxy.initiate_chat(
                self.assistant_agent,
                message=message,
                clear_history=False  # 保持对话历史
            )

            # 获取最后一条助手回复 - AutoGen 0.7.6格式
            if hasattr(self.user_proxy, 'chat_messages'):
                chat_history = self.user_proxy.chat_messages.get(self.assistant_agent, [])
                if chat_history:
                    last_message = chat_history[-1]
                    if isinstance(last_message, dict) and last_message.get("role") == "assistant":
                        return last_message.get("content", "")
                    elif hasattr(last_message, 'content'):
                        return last_message.content

            # 尝试从chat_result获取回复
            if chat_result and hasattr(chat_result, 'chat_history'):
                for msg in reversed(chat_result.chat_history):
                    if hasattr(msg, 'role') and msg.role == "assistant":
                        return msg.content if hasattr(msg, 'content') else str(msg)

            return "抱歉，我现在无法回复。请稍后再试。"

        except Exception as e:
            print(f"❌ AutoGen对话失败: {e}")
            print(f"详细错误: {traceback.format_exc()}")
            return f"对话出现错误: {str(e)}"
    
    def _split_response(self, response: str, chunk_size: int = 50) -> list:
        """将回复分割成块以模拟流式输出"""
        if not response:
            return [""]
            
        # 按字符分割，保持中文完整性
        chunks = []
        current_chunk = ""
        
        for char in response:
            current_chunk += char
            if len(current_chunk) >= chunk_size and char in "。！？\n ":
                chunks.append(current_chunk)
                current_chunk = ""
        
        # 添加剩余内容
        if current_chunk:
            chunks.append(current_chunk)
            
        return chunks if chunks else [response]
    
    def _format_stream_data(self, event_type: str, data: Dict[str, Any]) -> str:
        """格式化流式数据"""
        return json.dumps({
            "type": event_type,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }, ensure_ascii=False)
    
    def _format_error(self, error_message: str) -> str:
        """格式化错误信息"""
        return json.dumps({
            "type": "error",
            "data": {"message": error_message},
            "timestamp": datetime.now().isoformat()
        }, ensure_ascii=False)
    
    def get_conversation_history(self) -> list:
        """获取对话历史"""
        return self.conversation_history.copy()
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history.clear()
        if self.user_proxy and self.assistant_agent:
            # 清空AutoGen对话历史
            self.user_proxy.chat_messages.clear()
        print("🧹 对话历史已清空")
